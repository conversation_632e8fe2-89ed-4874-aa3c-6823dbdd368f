using System;
using System.Collections.Generic;
using DMS.Web.dms;

namespace DMS.Web.dms.Examples
{
    /// <summary>
    /// FtpHelper使用示例
    /// </summary>
    public class FtpHelperUsageExample
    {
        /// <summary>
        /// 基本使用示例
        /// </summary>
        public void BasicUsageExample()
        {
            // 创建FTP连接
            FtpHelper ftpHelper = new FtpHelper("192.168.1.100", "username", "password", 21, 30000);
            
            // 测试连接
            if (ftpHelper.TestConnection())
            {
                Console.WriteLine("FTP连接成功");
                
                // 目录操作示例
                DirectoryOperationsExample(ftpHelper);
                
                // 文件操作示例
                FileOperationsExample(ftpHelper);
                
                // 上传下载示例
                UploadDownloadExample(ftpHelper);
                
                // 断点续传示例
                ResumeTransferExample(ftpHelper);
            }
            else
            {
                Console.WriteLine("FTP连接失败");
            }
        }

        /// <summary>
        /// 目录操作示例
        /// </summary>
        /// <param name="ftpHelper">FTP帮助类实例</param>
        private void DirectoryOperationsExample(FtpHelper ftpHelper)
        {
            Console.WriteLine("=== 目录操作示例 ===");
            
            // 创建目录
            if (ftpHelper.CreateDirectory("/test_folder"))
            {
                Console.WriteLine("目录创建成功");
            }
            
            // 检查目录是否存在
            if (ftpHelper.DirectoryExists("/test_folder"))
            {
                Console.WriteLine("目录存在");
            }
            
            // 重命名目录
            if (ftpHelper.RenameDirectory("/test_folder", "/renamed_folder"))
            {
                Console.WriteLine("目录重命名成功");
            }
            
            // 获取目录列表
            List<string> directories = ftpHelper.GetDirectoryList("/");
            Console.WriteLine($"根目录下有 {directories.Count} 个项目");
            
            // 删除目录
            if (ftpHelper.DeleteDirectory("/renamed_folder"))
            {
                Console.WriteLine("目录删除成功");
            }
        }

        /// <summary>
        /// 文件操作示例
        /// </summary>
        /// <param name="ftpHelper">FTP帮助类实例</param>
        private void FileOperationsExample(FtpHelper ftpHelper)
        {
            Console.WriteLine("=== 文件操作示例 ===");
            
            string remoteFilePath = "/test_file.txt";
            
            // 检查文件是否存在
            if (ftpHelper.FileExists(remoteFilePath))
            {
                Console.WriteLine("文件存在");
                
                // 获取文件大小
                long fileSize = ftpHelper.GetFileSize(remoteFilePath);
                Console.WriteLine($"文件大小: {fileSize} 字节");
                
                // 重命名文件
                if (ftpHelper.RenameFile(remoteFilePath, "/renamed_file.txt"))
                {
                    Console.WriteLine("文件重命名成功");
                    remoteFilePath = "/renamed_file.txt";
                }
                
                // 删除文件
                if (ftpHelper.DeleteFile(remoteFilePath))
                {
                    Console.WriteLine("文件删除成功");
                }
            }
            
            // 获取文件列表
            List<string> files = ftpHelper.GetFileList("/");
            Console.WriteLine($"根目录下有 {files.Count} 个文件");
        }

        /// <summary>
        /// 上传下载示例
        /// </summary>
        /// <param name="ftpHelper">FTP帮助类实例</param>
        private void UploadDownloadExample(FtpHelper ftpHelper)
        {
            Console.WriteLine("=== 上传下载示例 ===");
            
            string localFilePath = @"C:\temp\test_file.txt";
            string remoteFilePath = "/uploaded_file.txt";
            string downloadPath = @"C:\temp\downloaded_file.txt";
            
            // 添加进度事件处理
            ftpHelper.UploadProgress += (bytesTransferred, totalBytes) =>
            {
                double percentage = (double)bytesTransferred / totalBytes * 100;
                Console.WriteLine($"上传进度: {percentage:F2}%");
            };
            
            ftpHelper.DownloadProgress += (bytesTransferred, totalBytes) =>
            {
                double percentage = (double)bytesTransferred / totalBytes * 100;
                Console.WriteLine($"下载进度: {percentage:F2}%");
            };
            
            // 上传文件
            if (ftpHelper.UploadFile(localFilePath, remoteFilePath))
            {
                Console.WriteLine("文件上传成功");
                
                // 下载文件
                if (ftpHelper.DownloadFile(remoteFilePath, downloadPath))
                {
                    Console.WriteLine("文件下载成功");
                }
            }
        }

        /// <summary>
        /// 断点续传示例
        /// </summary>
        /// <param name="ftpHelper">FTP帮助类实例</param>
        private void ResumeTransferExample(FtpHelper ftpHelper)
        {
            Console.WriteLine("=== 断点续传示例 ===");
            
            string localFilePath = @"C:\temp\large_file.zip";
            string remoteFilePath = "/large_file.zip";
            string downloadPath = @"C:\temp\downloaded_large_file.zip";
            
            // 断点续传上传
            if (ftpHelper.UploadFileWithResume(localFilePath, remoteFilePath, 3))
            {
                Console.WriteLine("断点续传上传成功");
                
                // 断点续传下载
                if (ftpHelper.DownloadFileWithResume(remoteFilePath, downloadPath, 3))
                {
                    Console.WriteLine("断点续传下载成功");
                }
            }
        }

        /// <summary>
        /// 批量操作示例
        /// </summary>
        /// <param name="ftpHelper">FTP帮助类实例</param>
        private void BatchOperationsExample(FtpHelper ftpHelper)
        {
            Console.WriteLine("=== 批量操作示例 ===");
            
            // 批量上传
            List<string> localFiles = new List<string>
            {
                @"C:\temp\file1.txt",
                @"C:\temp\file2.txt",
                @"C:\temp\file3.txt"
            };
            
            int uploadedCount = ftpHelper.BatchUploadFiles(localFiles, "/upload_folder", true);
            Console.WriteLine($"成功上传 {uploadedCount} 个文件");
            
            // 批量下载
            List<string> remoteFiles = new List<string>
            {
                "/upload_folder/file1.txt",
                "/upload_folder/file2.txt",
                "/upload_folder/file3.txt"
            };
            
            int downloadedCount = ftpHelper.BatchDownloadFiles(remoteFiles, @"C:\temp\downloads", true);
            Console.WriteLine($"成功下载 {downloadedCount} 个文件");
        }

        /// <summary>
        /// 错误处理示例
        /// </summary>
        /// <param name="ftpHelper">FTP帮助类实例</param>
        private void ErrorHandlingExample(FtpHelper ftpHelper)
        {
            Console.WriteLine("=== 错误处理示例 ===");
            
            try
            {
                // 尝试上传不存在的文件
                bool result = ftpHelper.UploadFile(@"C:\nonexistent\file.txt", "/test.txt");
                if (!result)
                {
                    Console.WriteLine("上传失败：文件不存在");
                }
            }
            catch (Exception ex)
            {
                string errorMessage = FtpHelper.GetDetailedErrorMessage(ex);
                Console.WriteLine($"上传异常: {errorMessage}");
            }
            
            // 获取系统信息
            string systemType = ftpHelper.GetSystemType();
            Console.WriteLine($"FTP服务器系统类型: {systemType}");
            
            string currentDir = ftpHelper.GetCurrentDirectory();
            Console.WriteLine($"当前工作目录: {currentDir}");
        }
    }
}
