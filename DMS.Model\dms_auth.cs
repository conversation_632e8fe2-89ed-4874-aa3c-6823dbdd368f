﻿using System;
namespace DMS.Model
{
	/// <summary>
	/// dms_auth:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class dms_auth
	{
		public dms_auth()
		{}
		#region Model
		private Guid _id;
		private int _columnid;
		private string _columnpath;
		private int _directoryid;
		private string _directorypath;
		private string _auth;
		private Guid _userid;
		private Guid _creator;
		private DateTime? _createtime;
		/// <summary>
		/// 编号
		/// </summary>
		public Guid Id
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 学校Id
		/// </summary>
		public int ColumnId
		{
			set{ _columnid=value;}
			get{return _columnid;}
		}
		/// <summary>
		/// 学校路径
		/// </summary>
		public string ColumnPath
		{
			set{ _columnpath=value;}
			get{return _columnpath;}
		}
		/// <summary>
		/// 所属文件夹
		/// </summary>
		public int DirectoryId
		{
			set{ _directoryid=value;}
			get{return _directoryid;}
		}
		/// <summary>
		/// 文件夹路径
		/// </summary>
		public string DirectoryPath
		{
			set{ _directorypath=value;}
			get{return _directorypath;}
		}
		/// <summary>
		/// 权限（1上传、2编辑、3删除，多权限|分割）
		/// </summary>
		public string Auth
		{
			set{ _auth=value;}
			get{return _auth;}
		}
		/// <summary>
		/// 被授权人
		/// </summary>
		public Guid UserId
		{
			set{ _userid=value;}
			get{return _userid;}
		}
		/// <summary>
		/// 授权人
		/// </summary>
		public Guid Creator
		{
			set{ _creator=value;}
			get{return _creator;}
		}
		/// <summary>
		/// 授权时间
		/// </summary>
		public DateTime? CreateTime
		{
			set{ _createtime=value;}
			get{return _createtime;}
		}
		#endregion Model

	}
}

