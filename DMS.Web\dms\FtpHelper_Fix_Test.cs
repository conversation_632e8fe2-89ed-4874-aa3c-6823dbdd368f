using System;
using System.Net;

namespace DMS.Web.dms
{
    /// <summary>
    /// FtpHelper修复验证测试
    /// </summary>
    public class FtpHelperFixTest
    {
        /// <summary>
        /// 验证WebRequestMethods.Ftp中可用的方法
        /// </summary>
        public void VerifyAvailableFtpMethods()
        {
            Console.WriteLine("=== .NET Framework 4.6 中可用的 WebRequestMethods.Ftp 方法 ===");
            
            // 这些是 .NET Framework 4.6 中实际可用的方法
            Console.WriteLine($"AppendFile: {WebRequestMethods.Ftp.AppendFile}");
            Console.WriteLine($"DeleteFile: {WebRequestMethods.Ftp.DeleteFile}");
            Console.WriteLine($"DownloadFile: {WebRequestMethods.Ftp.DownloadFile}");
            Console.WriteLine($"GetDateTimestamp: {WebRequestMethods.Ftp.GetDateTimestamp}");
            Console.WriteLine($"GetFileSize: {WebRequestMethods.Ftp.GetFileSize}");
            Console.WriteLine($"ListDirectory: {WebRequestMethods.Ftp.ListDirectory}");
            Console.WriteLine($"ListDirectoryDetails: {WebRequestMethods.Ftp.ListDirectoryDetails}");
            Console.WriteLine($"MakeDirectory: {WebRequestMethods.Ftp.MakeDirectory}");
            Console.WriteLine($"RemoveDirectory: {WebRequestMethods.Ftp.RemoveDirectory}");
            Console.WriteLine($"Rename: {WebRequestMethods.Ftp.Rename}");
            Console.WriteLine($"UploadFile: {WebRequestMethods.Ftp.UploadFile}");
            Console.WriteLine($"UploadFileWithUniqueName: {WebRequestMethods.Ftp.UploadFileWithUniqueName}");
            
            Console.WriteLine("\n=== 修复说明 ===");
            Console.WriteLine("1. GetSystemType 不存在 -> 使用自定义命令 'SYST'");
            Console.WriteLine("2. PrintWorkingDirectory 不存在 -> 使用自定义命令 'PWD'");
            Console.WriteLine("3. 其他所有方法都是标准的，无需修改");
        }

        /// <summary>
        /// 测试修复后的方法
        /// </summary>
        /// <param name="ftpHelper">FTP帮助类实例</param>
        public void TestFixedMethods(FtpHelper ftpHelper)
        {
            Console.WriteLine("\n=== 测试修复后的方法 ===");
            
            try
            {
                // 测试获取系统类型（使用 SYST 命令）
                string systemType = ftpHelper.GetSystemType();
                Console.WriteLine($"系统类型: {systemType}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取系统类型失败: {ex.Message}");
            }
            
            try
            {
                // 测试获取当前目录（使用 PWD 命令）
                string currentDir = ftpHelper.GetCurrentDirectory();
                Console.WriteLine($"当前目录: {currentDir}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取当前目录失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 完整的FTP方法测试
        /// </summary>
        /// <param name="server">FTP服务器</param>
        /// <param name="username">用户名</param>
        /// <param name="password">密码</param>
        public void FullFtpTest(string server, string username, string password)
        {
            Console.WriteLine("=== 完整的FTP功能测试 ===");
            
            FtpHelper ftpHelper = new FtpHelper(server, username, password);
            
            // 1. 测试连接
            Console.WriteLine("1. 测试连接...");
            if (ftpHelper.TestConnection())
            {
                Console.WriteLine("   ✓ 连接成功");
                
                // 2. 测试修复后的方法
                Console.WriteLine("2. 测试修复后的方法...");
                TestFixedMethods(ftpHelper);
                
                // 3. 测试基本操作
                Console.WriteLine("3. 测试基本操作...");
                TestBasicOperations(ftpHelper);
            }
            else
            {
                Console.WriteLine("   ✗ 连接失败");
            }
        }

        /// <summary>
        /// 测试基本操作
        /// </summary>
        /// <param name="ftpHelper">FTP帮助类实例</param>
        private void TestBasicOperations(FtpHelper ftpHelper)
        {
            try
            {
                // 测试目录操作
                string testDir = "/test_fix_" + DateTime.Now.ToString("yyyyMMddHHmmss");
                
                Console.WriteLine($"   创建测试目录: {testDir}");
                if (ftpHelper.CreateDirectory(testDir))
                {
                    Console.WriteLine("   ✓ 目录创建成功");
                    
                    // 检查目录是否存在
                    if (ftpHelper.DirectoryExists(testDir))
                    {
                        Console.WriteLine("   ✓ 目录存在验证成功");
                        
                        // 删除测试目录
                        if (ftpHelper.DeleteDirectory(testDir))
                        {
                            Console.WriteLine("   ✓ 目录删除成功");
                        }
                    }
                }
                
                // 测试获取目录列表
                var directories = ftpHelper.GetDirectoryList("/");
                Console.WriteLine($"   根目录包含 {directories.Count} 个项目");
                
                // 测试获取文件列表
                var files = ftpHelper.GetFileList("/");
                Console.WriteLine($"   根目录包含 {files.Count} 个文件");
                
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ✗ 基本操作测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 显示修复前后的对比
        /// </summary>
        public void ShowFixComparison()
        {
            Console.WriteLine("=== 修复前后对比 ===");
            Console.WriteLine();
            
            Console.WriteLine("修复前（错误）:");
            Console.WriteLine("  WebRequestMethods.Ftp.GetSystemType     // ❌ 不存在");
            Console.WriteLine("  WebRequestMethods.Ftp.PrintWorkingDirectory // ❌ 不存在");
            Console.WriteLine();
            
            Console.WriteLine("修复后（正确）:");
            Console.WriteLine("  \"SYST\"  // ✅ 自定义FTP命令获取系统类型");
            Console.WriteLine("  \"PWD\"   // ✅ 自定义FTP命令获取当前目录");
            Console.WriteLine();
            
            Console.WriteLine("说明:");
            Console.WriteLine("- .NET Framework 4.6 的 WebRequestMethods.Ftp 类不包含这两个方法");
            Console.WriteLine("- 使用自定义FTP命令字符串可以实现相同功能");
            Console.WriteLine("- SYST 命令用于获取FTP服务器系统信息");
            Console.WriteLine("- PWD 命令用于获取当前工作目录路径");
        }
    }
}
