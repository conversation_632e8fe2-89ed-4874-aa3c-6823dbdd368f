﻿/*开关*/
.single-checkbox { display: inline-block; vertical-align: middle; cursor: pointer; }
    .single-checkbox a, .single-checkbox a:visited { display: inline-block; border: 1px solid #d4d4d4; background: #d4d4d4; width: 80px; vertical-align: middle; text-decoration: none; }
        .single-checkbox a.selected { border: 1px solid #16a0d3; background: #16a0d3; }
        .single-checkbox a i { display: block; width: 50%; height: 28px; line-height: 28px; font-style: normal; background: #fff; color: #333; text-align: center; }
        .single-checkbox a.selected i.off { display: none; }
        .single-checkbox a.selected i.on { display: block; }
        .single-checkbox a i.off { float: left; display: block; }
        .single-checkbox a i.on { float: right; display: none; }
/*上传文件*/
#file_list { display: inline-block; }
.file_ul { margin-top: 0; padding: 0; list-style-type: none; }
    .file_ul li { float: left; margin: 0 2px; padding-left: 2px; }
        .file_ul li .divfile { margin: 2px; padding: 5px 4px; height: 120px; border-radius: 5px; background-color: #efefef; }
            .file_ul li .divfile:hover { background-color: #DADAD9; }
        .file_ul li a { color: #000; text-decoration: none; }
#file_list .file_delete { position: relative; top: -125px; right: -92px; bottom: 128px; display: block; margin-left: 16px; width: 18px; height: 18px; background: url(/js/jquery.uploadify-v3.1/uploadify-cancel.png) no-repeat; cursor: pointer; }
.fileImg { display: block; margin: 0 auto; width: 100%; height: 100%; }
    .fileImg div { font-size: 15px; display: flex; justify-content: center; align-items: center; width: 80%; height: 100%; background-color: darkcyan; color: white; font-weight: bold; margin: 0 auto; }
.append { display: block; padding: 3px; color: grey; }
/*选项卡*/
* { padding: 0; margin: 0; font-family: "Microsoft YaHei"; }
a:link, a:visited { color: #2A72C5; text-decoration: none; }
.content-tab { z-index: 3; top: 0; left: 15px; right: 15px; bottom: auto; height: 41px; background: #fff; border-bottom: 1px solid #eee; }
.content-tab-ul-wrap { position: relative; }
.content-tab .tab-title { display: none; }
.content-tab ul { position: absolute; left: 0; top: 10px; width: 100%; }
    .content-tab ul li { position: relative; display: block; float: left; margin-right: -1px; }
        .content-tab ul li a { display: block; float: left; border-top: 1px solid #eee; border-right: 1px solid #eee; border-left: 1px solid #eee; height: 30px; line-height: 31px; font-size: 12px; color: #333; text-align: center; background: #fff; white-space: nowrap; word-break: break-all; padding: 0 25px; }
            .content-tab ul li a.selected { color: #2A72C5; background: #fff; height: 31px; }
.tab-content { padding: 15px; font-size: 12px; color: #666; border: 1px solid #eee; border-top: none; box-sizing: border-box; /* overflow: hidden; */ min-height: 330px; }
    .tab-content dl:first-child { border-top: none; }
    .tab-content dl, .div-content dl { clear: both; display: block; padding: 5px 0; line-height: 30px; }
        .tab-content dl dt { display: block; float: left; width: 80px; text-align: right; color: #6d7e86; }
        .tab-content dl dd { position: relative; margin-left: 100px; }
.red { color: red; margin-right: 5px; }
/*多文件*/
.jFiler-items.jFiler-row { height: 270px; overflow-y: auto; position: relative; margin-top: 10px; }
.jFiler-input-dragDrop a { color: #97A1A8; }
.jFiler-input-dragDrop p { word-break:break-all}
