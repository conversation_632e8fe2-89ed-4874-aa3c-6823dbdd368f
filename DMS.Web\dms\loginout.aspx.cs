using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace DMS.Web.dms
{
    public partial class loginout : System.Web.UI.Page
    {
        BasePage ac = new BasePage();
        protected void Page_Load(object sender, EventArgs e)
        {
            try
            {
                if (User.Identity.IsAuthenticated)
                {
                    //写入日志
                    string _userId = Membership.GetUser(User.Identity.Name).ProviderUserKey.ToString();
                    YunEdu.BLL.Site_Logs bllLogs = new YunEdu.BLL.Site_Logs();
                    YunEdu.Model.Site_Logs modelLogs = new YunEdu.Model.Site_Logs();
                    modelLogs.TbName = "无";
                    modelLogs.PrimaryKeyValue = "无";
                    modelLogs.OtherKeyValue = "无";
                    modelLogs.ModifyFields = "无";
                    modelLogs.BeforeValue = "无";
                    modelLogs.ModifiedValue = "无";
                    modelLogs.UserID = new Guid(_userId);
                    modelLogs.Module = "退出登录";
                    modelLogs.UserIP = Request.UserHostAddress;
                    bllLogs.Add(modelLogs);
                }
            }
            catch (Exception ex)
            {

            }
            finally
            {
                FormsAuthentication.SignOut();
                //清除Session
                Session["AuthorityStr"] = null;
                Session.Clear();
                Session.Abandon();
                //清除cookie
                HttpCookie aCookie;
                string cookieName;
                int limit = Request.Cookies.Count;
                for (int i = 0; i < limit; i++)
                {
                    cookieName = Request.Cookies[i].Name;
                    aCookie = new HttpCookie(cookieName);
                    aCookie.Value = "";
                    aCookie.Expires = DateTime.Now.AddDays(-1);
                    Response.Cookies.Add(aCookie);
                }
                Response.Redirect("~/login.aspx");
            }
        }
    }
}