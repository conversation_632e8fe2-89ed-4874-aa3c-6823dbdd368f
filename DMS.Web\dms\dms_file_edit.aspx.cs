﻿using System;
using System.Linq;
using YunEdu.Common.DEncrypt;
using YunEdu.Common;
using System.Text.RegularExpressions;

namespace DMS.Web.dms
{
    public partial class dms_file_edit : BasePage
    {
        Guid Id = Guid.Empty;
        BLL.dms_files bllfiles = new BLL.dms_files();

        protected void Page_Load(object sender, EventArgs e)
        {
            if (!string.IsNullOrEmpty(Request.QueryString["Id"])) Guid.TryParse(Request.QueryString["Id"], out Id);

            if (!IsPostBack)
            {
                string _domain = DESEncrypt.Encrypt(Request.Url.Authority.ToLower(), CodeTable.EncryptKeySys);
                Show();
            }
        }

        /// <summary>
        /// 展示编辑信息
        /// </summary>
        private void Show()
        {
            if (Id == Guid.Empty)
            {
                MessageBox.ResponseScript(this, "showMessage('true','当前文件不存在！',0)");
                return;
            }
            var modelfiles = bllfiles.GetModel(Id);
            if (modelfiles == null)
            {
                MessageBox.ResponseScript(this, "showMessage('true','当前文件不存在！',0)");
                return;
            }
            //编辑
            txtTitle.Text = modelfiles.FileName;
            txtContents.Text = modelfiles.Memo;
            chkIsOpen.Checked = modelfiles.IsOpen == 1;
        }

        // 保存按钮点击事件
        protected void btnSave_Click(object sender, EventArgs e)
        {
            var modelfiles = bllfiles.GetModel(Id);
            if (modelfiles == null)
            {
                MessageBox.ResponseScript(this, "showMessage('true','当前文件不存在！',0)");
                return;
            }
            //判断用户是否有添加权限
            Model.dms_auth modelAuth = new BLL.dms_auth().GetModel(new Guid(UserId), modelfiles.DirectoryId);
            if (!(modelAuth != null && !string.IsNullOrEmpty(modelAuth.Auth) && modelAuth.Auth.IndexOf("1") > -1)
                && !RoleLevel.Contains(((int)CodeTable.AdminRoleLevel.SchoolAdmin).ToString()))
            {
                MessageBox.ResponseScript(this, "showMessage('false','您当前没有添加文件权限，请联系管理员！',0)");
                return;
            }
            string newFileName = txtTitle.Text.Contains("." + modelfiles.FileFormat) ? txtTitle.Text.Trim() : txtTitle.Text.Trim() + "." + modelfiles.FileFormat;
            modelfiles.FileName = newFileName;
            modelfiles.Memo = Regex.Replace(txtContents.Text, @"<(.[^>]*)>", string.Empty, RegexOptions.IgnoreCase);
            modelfiles.IsOpen = chkIsOpen.Checked ? 1 : 0;
            bllfiles.Update(modelfiles);
            MessageBox.ResponseScript(this, "showMessage('true','保存成功！',1)");
        }
    }
}