﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;

namespace DMS.BLL
{
    /// <summary>
    /// 数据访问类:dms_config
    /// </summary>
    public partial class dms_config
    {
        public dms_config()
        { }
        #region  BasicMethod

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(Guid Id)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from dms_config");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }


        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(DMS.Model.dms_config model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into dms_config(");
            strSql.Append("Id,ColumnId,ColumnPath,ServerUrl,FtpPort,FtpAccount,FtpPassword,LastEditBy,LastEditTime)");
            strSql.Append(" values (");
            strSql.Append("@Id,@ColumnId,@ColumnPath,@ServerUrl,@FtpPort,@FtpAccount,@FtpPassword,@LastEditBy,@LastEditTime)");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,150),
                    new SqlParameter("@ServerUrl", SqlDbType.NVarChar,150),
                    new SqlParameter("@FtpPort", SqlDbType.Int,4),
                    new SqlParameter("@FtpAccount", SqlDbType.NVarChar,50),
                    new SqlParameter("@FtpPassword", SqlDbType.NVarChar,50),
                    new SqlParameter("@LastEditBy", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@LastEditTime", SqlDbType.DateTime)};
            parameters[0].Value = model.Id = Guid.NewGuid();
            parameters[1].Value = model.ColumnId;
            parameters[2].Value = model.ColumnPath;
            parameters[3].Value = model.ServerUrl;
            parameters[4].Value = model.FtpPort;
            parameters[5].Value = model.FtpAccount;
            parameters[6].Value = model.FtpPassword;
            parameters[7].Value = model.LastEditBy;
            parameters[8].Value = model.LastEditTime;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(DMS.Model.dms_config model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update dms_config set ");
            strSql.Append("ColumnId=@ColumnId,");
            strSql.Append("ColumnPath=@ColumnPath,");
            strSql.Append("ServerUrl=@ServerUrl,");
            strSql.Append("FtpPort=@FtpPort,");
            strSql.Append("FtpAccount=@FtpAccount,");
            strSql.Append("FtpPassword=@FtpPassword,");
            strSql.Append("LastEditBy=@LastEditBy,");
            strSql.Append("LastEditTime=@LastEditTime");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ColumnId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar,150),
                    new SqlParameter("@ServerUrl", SqlDbType.NVarChar,150),
                    new SqlParameter("@FtpPort", SqlDbType.Int,4),
                    new SqlParameter("@FtpAccount", SqlDbType.NVarChar,50),
                    new SqlParameter("@FtpPassword", SqlDbType.NVarChar,50),
                    new SqlParameter("@LastEditBy", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@LastEditTime", SqlDbType.DateTime),
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = model.ColumnId;
            parameters[1].Value = model.ColumnPath;
            parameters[2].Value = model.ServerUrl;
            parameters[3].Value = model.FtpPort;
            parameters[4].Value = model.FtpAccount;
            parameters[5].Value = model.FtpPassword;
            parameters[6].Value = model.LastEditBy;
            parameters[7].Value = model.LastEditTime;
            parameters[8].Value = model.Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(Guid Id)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from dms_config ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string Idlist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from dms_config ");
            strSql.Append(" where Id in (" + Idlist + ")  ");
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public DMS.Model.dms_config GetModel(Guid Id)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 Id,ColumnId,ColumnPath,ServerUrl,FtpPort,FtpAccount,FtpPassword,LastEditBy,LastEditTime from dms_config ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)          };
            parameters[0].Value = Id;

            DMS.Model.dms_config model = new DMS.Model.dms_config();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }

        /// <summary>
        /// 根据ColumnId得到一个对象实体
        /// </summary>
        public DMS.Model.dms_config GetModelByColumnId(int columnId)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 Id,ColumnId,ColumnPath,ServerUrl,FtpPort,FtpAccount,FtpPassword,LastEditBy,LastEditTime from dms_config ");
            strSql.Append(" where ColumnId=@ColumnId ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ColumnId", SqlDbType.Int,4)          };
            parameters[0].Value = columnId;

            DMS.Model.dms_config model = new DMS.Model.dms_config();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }

        /// <summary>
        /// 根据ColumnId检查是否存在配置
        /// </summary>
        public bool ExistsByColumnId(int columnId)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from dms_config");
            strSql.Append(" where ColumnId=@ColumnId ");
            SqlParameter[] parameters = {
                    new SqlParameter("@ColumnId", SqlDbType.Int,4)          };
            parameters[0].Value = columnId;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }

        /// <summary>
        /// 根据ColumnPath向上查找FTP配置
        /// </summary>
        /// <param name="columnPath">地区路径，例如：1|2|3|4</param>
        /// <returns>配置信息DataSet</returns>
        public DataSet GetConfigByColumnPath(string columnPath)
        {
            if (string.IsNullOrEmpty(columnPath))
                return null;

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select Id,ColumnId,ColumnPath,ServerUrl,FtpPort,FtpAccount,FtpPassword,LastEditBy,LastEditTime from dms_config ");
            strSql.Append(" where @ColumnPath+'|' like ColumnPath+'|%' order by ColumnPath desc ");

            SqlParameter[] parameters = {
                    new SqlParameter("@ColumnPath", SqlDbType.NVarChar, 500)
            };
            parameters[0].Value = columnPath;

            return DbHelperSQL.Query(strSql.ToString(), parameters);
        }

        /// <summary>
        /// 根据ColumnPath数组向上查找FTP配置
        /// </summary>
        /// <param name="columnPath">地区id路径</param>
        /// <returns>第一个找到的配置信息</returns>
        public DMS.Model.dms_config GetModel(string columnPath)
        {
            DataSet ds = GetConfigByColumnPath(columnPath);
            if (ds != null && ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            return null;
        }

        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public DMS.Model.dms_config DataRowToModel(DataRow row)
        {
            DMS.Model.dms_config model = new DMS.Model.dms_config();
            if (row != null)
            {
                if (row["Id"] != null && row["Id"].ToString() != "")
                {
                    model.Id = new Guid(row["Id"].ToString());
                }
                if (row["ColumnId"] != null && row["ColumnId"].ToString() != "")
                {
                    model.ColumnId = int.Parse(row["ColumnId"].ToString());
                }
                if (row["ColumnPath"] != null)
                {
                    model.ColumnPath = row["ColumnPath"].ToString();
                }
                if (row["ServerUrl"] != null)
                {
                    model.ServerUrl = row["ServerUrl"].ToString();
                }
                if (row["FtpPort"] != null && row["FtpPort"].ToString() != "")
                {
                    model.FtpPort = int.Parse(row["FtpPort"].ToString());
                }
                if (row["FtpAccount"] != null)
                {
                    model.FtpAccount = row["FtpAccount"].ToString();
                }
                if (row["FtpPassword"] != null)
                {
                    model.FtpPassword = row["FtpPassword"].ToString();
                }
                if (row["LastEditBy"] != null && row["LastEditBy"].ToString() != "")
                {
                    model.LastEditBy = new Guid(row["LastEditBy"].ToString());
                }
                if (row["LastEditTime"] != null && row["LastEditTime"].ToString() != "")
                {
                    model.LastEditTime = DateTime.Parse(row["LastEditTime"].ToString());
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select Id,ColumnId,ColumnPath,ServerUrl,FtpPort,FtpAccount,FtpPassword,LastEditBy,LastEditTime ");
            strSql.Append(" FROM dms_config ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" Id,ColumnId,ColumnPath,ServerUrl,FtpPort,FtpAccount,FtpPassword,LastEditBy,LastEditTime ");
            strSql.Append(" FROM dms_config ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM dms_config ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.Id desc");
            }
            strSql.Append(")AS Row, T.*  from dms_config T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "dms_config";
			parameters[1].Value = "Id";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

        #endregion  BasicMethod
        #region  ExtensionMethod

        #endregion  ExtensionMethod
    }
}

