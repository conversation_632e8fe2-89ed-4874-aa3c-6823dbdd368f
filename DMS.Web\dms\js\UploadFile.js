﻿(function ($) {


    //上传文件列表
    $.uploadFile_List = new Array();
    var _num;
    //
    $.uploadFile = function (documentId, directory, fileTypeExts, num, multi, fileType, fileSize, fileList) {

        //判断是否支持flash
        var fls = flashChecker();
        if (!fls.f) {
            alert("当前浏览器没有开启或不支持flash,将会导致图片附件上传失败！");
            //$(documentId).hide();
            $(documentId).parent().html('<a href="https://get.adobe.com/cn/flashplayer/"; style="border:1px solid #adaaad; padding: 5px; border-radius: 4px; color: #3453cc;text-decoration: none;" target="_blank">安装或者启用FLASH播放器</a>');
        }
        var ext = ".gif;*.jpg;*.jpeg;*.png;";
        var nameList = fileList.split('|');
        //自动显示相册
        for (var i = 0; i < $.uploadFile_List.length; i++) {
            var path = $.uploadFile_List[i].Path;
            var format = $.uploadFile_List[i].Format;
            var name = nameList[i];
            var photoList = $(documentId).next("#file_list");
            if (photoList.length == 0) {
                photoList = $("<div id=\"file_list\"><ul class=\"file_ul\"></ul></div>");

                $(documentId).parent().append(photoList);

            }
            var ul = photoList.find("ul");
            var photo = "<li><div class=\"divfile\">";

            if (ext.indexOf(format) == -1) {
                photo += "<div class='fileImg' src=" + path + "><div>" + format + "</div></div><span class=\"file_delete\" title=\"删除\">&nbsp;</span>";
            } else {
                photo += "<img class='fileImg' src=" + path + " onclick='FileImg(this,\"" + nameList[i] + "\")' /><span class=\"file_delete\" title=\"删除\">&nbsp;</span>";
            }

            photo += "</div>";
            photo += "</li>";
            ul.append(photo);
        }
        //允许多文件上传
        if (multi == undefined) {
            multi = true;//默认
        }
        //声明上传文件对象
        function UploadFile(name, format, path, size) {
            this.Name = name;//名称
            this.Format = format;//格式
            this.Path = path;//路径
            this.Size = size;
        };

        $(documentId).uploadify({
            //指定swf文件
            'swf': '/js/jquery.uploadify-v3.1/uploadify.swf',
            //后台处理的页面
            'uploader': '/dms/ajax/UploadFile.ashx',
            //按钮显示的文字
            'buttonText': '上传附件',
            //显示的高度和宽度，默认 height 30；width 120
            'height': 22,
            'width': 60,
            //上传文件的类型  默认为所有文件    'All Files'  ;  '*.*'
            //在浏览窗口底部的文件类型下拉菜单中显示的文本
            'fileTypeDesc': 'All Files',
            //允许上传的文件后缀
            'fileTypeExts': fileTypeExts,
            //发送给后台的其他参数通过formData指定
            'formData': { 'folder': directory },
            //上传文件页面中，你想要用来作为文件队列的元素的id, 默认为false  自动生成,  不带#
            //'queueID': 'fileQueue',
            //选择文件后自动上传
            'auto': true,
            //设置为true将允许多文件上传
            'multi': multi,
            'fileSizeLimit': fileSize+"B",
            'uploadLimit': num, //上传个数
            'overrideEvents': ['onSelectError', 'onDialogClose', 'onUploadError'],
            //'debug': true,
            'removeCompleted': true,
            'onSelect': function (fileObj) {
                $("#txtTitle").val(fileObj.name);
                var _fileSize = 0;
                if (fileSize == "" || fileSize == "undefined") {
                    fileSize = "120M";
                }
                if (fileSize.toLowerCase().indexOf("m") > -1) {
                    _fileSize = fileSize.substring(0, fileSize.length - 1) * 1024 * 1024;
                }
                if (fileObj.size > _fileSize) {
                    $(documentId).uploadify('cancel', fileObj.id);
                    layer.msg("文件太大！请上传不超过" + fileSize + "B的文件");
                }
                if (fileType == "" || fileType == "undefined") {
                    fileType = "*.rar,*.zip,*.png,*.jpeg,*.gif,*.doc,*.xls,*.docx";
                }
                if (fileType.indexOf(fileObj.type.toLowerCase()) == -1) {
                    $("#txtTitle").val("");
                    $(documentId).uploadify('cancel')
                    layer.msg("请上传符合规定的类型:" + fileType);
                }
                if ($(".divfile").length >= num) {
                    $(documentId).uploadify('cancel')
                    layer.msg("上传的文件数量已经超出系统限制的" + num + "个文件！");
                }
            },
            'onUploadSuccess': function (file, path, response) {

                //校宣模式不符合尺寸提示
                if (path.indexOf("图片尺寸不符合要求") > -1) {
                    layer.msg(path);
                } else {
                    if (file.size == 0) {
                        return;
                    }
                    var fileName = file.name.replace(file.type, "");
                    //显示相册
                    var photoList = this.queue.next("#file_list");


                    if (!multi) {
                        //单个文件上传
                        photoList.remove();
                        photoList.length = 0;
                        $.uploadFile_List = new Array();
                    }
                    if (photoList.length == 0) {
                        photoList = $("<div id=\"file_list\"><ul class=\"file_ul\"></ul></div>");
                        this.queue.parent().append(photoList);

                    }
                    var ul = photoList.find("ul");
                    var photo = "<li><div class=\"divfile\">";
                    var name = fileName + file.type;
                    if (ext.indexOf(file.type.toLowerCase()) == -1) {
                        photo += "<div class='fileImg' src=" + path + "><div>" + file.type + "</div></div><span class=\"file_delete\" title=\"删除\">&nbsp;</span>";
                    } else {
                        photo += "<img class='fileImg' src=" + path + " onclick='FileImg(this,\"" + name + "\")' /><span class=\"file_delete\" title=\"删除\">&nbsp;</span>";
                    }

                    photo += "</div>";
                    photo += "</li>";
                    ul.append(photo);
                    //相册参数
                    $.uploadFile_List[$.uploadFile_List.length] = new UploadFile(name, file.type, path, file.size);
                }
            },
            'onSelectError': function (file, errorCode, errorMsg) {
                switch (errorCode) {
                    case -100:
                        layer.msg("上传的文件数量已经超出系统限制的" + num + "个文件！");
                        return false;
                        break;
                    case -110:
                        layer.msg("文件 [" + file.name + "]文件太大！请上传不超过" + fileSize + "B的文件!");
                        return false;
                        break;
                    case -120:
                        layer.msg("文件 [" + file.name + "] 大小异常！");
                        break;
                    case -130:
                        layer.msg("文件 [" + file.name + "] 类型不正确！");
                        break;
                }
            }
        });
    }

    //删除按钮不显示方法
    $.uploadFile.delHide = function () {
        $("#file_list .file_delete").remove();
    }
})(jQuery);

$(function () {
    //相册标签Img删除事件
    $(".divfile  .file_delete").live("click", function () {
        var img = $(this).siblings();
        var src = img.attr("src");
        $.ajax({
            type: "post",
            url: "/dms/ajax/UploadFile.ashx",
            data: { "type": "del", "picurl": src },
            async: false,
            success: function (msg) {
                if (msg == src) {
                    $("#txtTitle").val("");
                    img.parent().parent().remove();
                    var swfuploadify = $('#uploadify').data("uploadify");
                    var stats = swfuploadify.getStats();
                    stats.successful_uploads--;
                    swfuploadify.setStats(stats);
                    for (var i = 0; i < $.uploadFile_List.length; i++) {
                        if ($.uploadFile_List[i].Path != undefined && $.uploadFile_List[i].Path == src) {
                            $.uploadFile_List.splice(i, 1);
                            break;
                        }
                    }
                }
            }
        });
    });
});
function flashChecker() {
    var hasFlash = 0; //是否安装了flash
    var flashVersion = 0; //flash版本
    if (document.all) {
        var swf = new ActiveXObject('ShockwaveFlash.ShockwaveFlash');
        if (swf) {
            hasFlash = 1;
            VSwf = swf.GetVariable("$version");
            flashVersion = parseInt(VSwf.split(" ")[1].split(",")[0]);
        }
    } else {
        if (navigator.plugins && navigator.plugins.length > 0) {
            var swf = navigator.plugins["Shockwave Flash"];
            if (swf) {
                hasFlash = 1;
                var words = swf.description.split(" ");
                for (var i = 0; i < words.length; ++i) {
                    if (isNaN(parseInt(words[i]))) continue;
                    flashVersion = parseInt(words[i]);
                }
            }
        }
    }
    return { f: hasFlash, v: flashVersion };
}