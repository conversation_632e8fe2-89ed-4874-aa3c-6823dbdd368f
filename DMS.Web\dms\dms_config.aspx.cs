﻿using System;

namespace DMS.Web.dms
{
    public partial class dms_config : BasePage
    {
        private YunEdu.Authority.AdminCommonJC ac = new YunEdu.Authority.AdminCommonJC();
        private DMS.BLL.dms_config bllConfig = new DMS.BLL.dms_config();

        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                LoadConfigInfo();
            }
        }

        /// <summary>
        /// 加载配置信息
        /// </summary>
        private void LoadConfigInfo()
        {
            try
            {
                // 获取当前用户所在地区ID
                int columnId = ac.modelAreaUser.ColumnID;

                // 根据地区ID获取配置信息
                DMS.Model.dms_config model = bllConfig.GetModelByColumnId(columnId);

                if (model != null)
                {
                    // 显示配置信息
                    pnlConfig.Visible = true;
                    pnlNoConfig.Visible = false;

                    litServerUrl.Text = string.IsNullOrEmpty(model.ServerUrl) ? "/" : model.ServerUrl;

                    // 判断是否为FTP地址
                    if (IsFtpUrl(model.ServerUrl))
                    {
                        litFtpPort.Text = model.FtpPort.ToString();
                        litFtpAccount.Text = string.IsNullOrEmpty(model.FtpAccount) ? "未设置" : model.FtpAccount;
                        litFtpPassword.Text = string.IsNullOrEmpty(model.FtpPassword) ? "未设置" : "******";
                    }
                    else
                    {
                        litFtpPort.Text = "不适用";
                        litFtpAccount.Text = "不适用";
                        litFtpPassword.Text = "不适用";
                    }

                    litLastEditTime.Text = model.LastEditTime.ToString("yyyy-MM-dd HH:mm:ss");
                }
                else
                {
                    // 没有配置信息
                    pnlConfig.Visible = false;
                    pnlNoConfig.Visible = true;
                }
            }
            catch (Exception ex)
            {
                // 错误处理
                Response.Write($"<script>alert('加载配置信息失败：{ex.Message}');</script>");
            }
        }

        /// <summary>
        /// 判断是否为FTP地址
        /// </summary>
        /// <param name="url">URL地址</param>
        /// <returns>是否为FTP地址</returns>
        private bool IsFtpUrl(string url)
        {
            if (string.IsNullOrEmpty(url))
                return false;

            return url.ToLower().StartsWith("ftp://") || url.ToLower().Contains("ftp");
        }

        protected void btnSearch_Click(object sender, EventArgs e)
        {
            LoadConfigInfo();
        }
    }
}