﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="dms_file_edit.aspx.cs" Inherits="DMS.Web.dms.dms_file_edit" ValidateRequest="false" StylesheetTheme="Admin_Default" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>修改文件信息</title>
    <link href="css/dms_file_Add.css" rel="stylesheet" />
    <script src="/js/jquery-1.8.3.min.js" type="text/javascript"></script>
    <script src="/js/layer/layer.js" type="text/javascript"></script>
    <script src="/admin/js/Common.js" type="text/javascript"></script>
    <style>
        .tab-content { border: none; }
    </style>
    <script>
        function CloseBox() {
            parent.layer.close(parent.layer.getFrameIndex(window.name));
        };
        function showMessage(success, content, ico) {
            var _index = parent.layer.alert(content, {
                icon: ico,
                shade: ['0.1', '#000'],
                time: success == "true" ? 1000 : 0,
                end: function () {
                    if (success == "true") {
                        parent.refreshData();
                        CloseBox();
                    }

                }
            });
        }
    </script>
</head>
<body>
    <form id="form1" runat="server">
        <div id="container">
            <div class="tab-content">
                <div id="list" class="infoTable margin-bottom-bar">
                    <dl>
                        <dt>
                            <span class="red">*</span>文件标题
                        </dt>
                        <dd>
                            <asp:TextBox ID="txtTitle" runat="server" Width="200px"></asp:TextBox>
                        </dd>
                    </dl>
                    <dl>
                        <dt>文件描述</dt>
                        <dd>
                            <div class="act content" style="width: 99%;">
                                <asp:TextBox ID="txtContents" runat="server" Width="100%" Height="150px"
                                    TextMode="MultiLine"></asp:TextBox>
                                <asp:HiddenField ID="hidContent" runat="server" />
                            </div>
                        </dd>
                    </dl>
                    <dl>
                        <dt>是否公开</dt>
                        <dd>
                            <div class="rule-single-checkbox single-checkbox chkIsOpen">
                                <a href="javascript:void(0);"><i class="off">否</i><i class="on">是</i></a>
                                <asp:CheckBox ID="chkIsOpen" runat="server" Text="是" Style="display: none;" />
                            </div>
                        </dd>
                    </dl>
                </div>
            </div>
            <div class="bottom-bar">
                <input id="btnAdd" type="button" value="保存" class="btnGreen" />
                <asp:Button ID="btnSave" runat="server" Text="保存" CssClass="btnGreen" OnClick="btnSave_Click"
                    Style="display: none" />&nbsp;
                    <input id="btnClose" type="button" value="关闭" class="btnGray" onclick="CloseBox()" />
            </div>
        </div>
    </form>
</body>
<script>
    $(function () {
        //是否公开
        if ($("#chkIsOpen").attr("checked") == "checked")
            $(".rule-single-checkbox.single-checkbox.chkIsOpen a").addClass("selected");

        $(".rule-single-checkbox.single-checkbox.chkIsOpen").click(function () {
            if ($(this).find("a").toggleClass("selected").is(".selected")) {
                $("#chkIsOpen").attr("checked", true);
            }
            else {
                $("#chkIsOpen").attr("checked", false);
            }
        });
    })
    //添加
    $("#btnAdd").click(function () {
        if ($('#txtTitle').val() == "") {
            layer.msg('文件标题不能为空');
            return false;
        }
        $("#btnSave").click();
    })
</script>
</html>
