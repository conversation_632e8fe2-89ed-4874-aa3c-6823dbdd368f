﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="dms_config_edit.aspx.cs" Inherits="DMS.Web.dms.dms_config_edit" StylesheetTheme="Admin_Default" EnableEventValidation="false" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>编辑文档管理系统配置</title>
    <script src="/js/jquery-1.8.3.min.js"></script>
    <script src="/admin/js/Common.js"></script>
    <script src="/js/layer/layer.js"></script>
    <style>
        .help-text { font-size: 12px; color: #666; margin-top: 3px; }
    </style>
    <script>
        function toggleFtpFields() {
            var serverUrl = $('#txtServerUrl').val().toLowerCase();

            if (serverUrl.indexOf('ftp://') === 0 || serverUrl.indexOf('ftp') !== -1) {
                $('.ftp-fields').removeClass('hide');

            } else {
                $('.ftp-fields').addClass('hide');
            }
        }

        function validateForm() {
            var serverUrl = $('#txtServerUrl').val().toLowerCase();
            if (serverUrl === '') {
                layer.msg('请输入服务器地址');
                return false;
            }

            if (serverUrl.indexOf('ftp://') === 0 || serverUrl.indexOf('ftp') !== -1) {
                var ftpPort = $('#txtFtpPort').val();
                var ftpAccount = $('#txtFtpAccount').val();
                var ftpPassword = $('#txtFtpPassword').val();

                if (ftpPort === '' || ftpAccount === '' || ftpPassword === '') {
                    layer.msg('FTP地址需要填写端口、账号和密码信息');
                    return false;
                }

                var port = parseInt(ftpPort);
                if (isNaN(port) || port < 1 || port > 65535) {
                    layer.msg('请输入有效的端口号(1-65535)');
                    return false;
                }
            }

            return true;
        }

        function CloseBox(refresh) {
            CloseWindow();
            if (refresh) {
                parent.__doPostBack('btnSearch', '1');
            }
        }

        // 测试FTP连接
        function testFtpConnection() {
            var serverUrl = $('#txtServerUrl').val().trim();
            var ftpPort = $('#txtFtpPort').val().trim();
            var ftpAccount = $('#txtFtpAccount').val().trim();
            var ftpPassword = $('#txtFtpPassword').val().trim();

            // 验证输入
            if (serverUrl === '' || ftpPort === '' || ftpAccount === '' || ftpPassword === '') {
                layer.msg('请先填写完整的FTP连接信息');
                return;
            }

            var port = parseInt(ftpPort);
            if (isNaN(port) || port < 1 || port > 65535) {
                layer.msg('请输入有效的端口号(1-65535)');
                return;
            }

            // 显示测试中状态
            $('#btnTestConnection').prop('disabled', true).val('测试中...');
            $('#testResult').hide();

            // 发送AJAX请求
            $.ajax({
                url: 'ajax/dms_file_manage.ashx',
                type: 'POST',
                data: {
                    action: 'testFtpConnection',
                    serverUrl: serverUrl,
                    ftpPort: ftpPort,
                    ftpAccount: ftpAccount,
                    ftpPassword: ftpPassword
                },
                dataType: 'json',
                success: function (result) {
                    $('#btnTestConnection').prop('disabled', false).val('测试FTP连接');
                    if (result.success) {
                        $('#testResult').html('<span style="color: green;">FTP连接测试成功！</span>').show();
                    } else {
                        $('#testResult').html('<span style="color: red;">FTP连接测试失败：' + result.message + '</span>').show();
                    }
                },
                error: function (xhr, status, error) {
                    $('#btnTestConnection').prop('disabled', false).val('测试FTP连接');
                    $('#testResult').html('<span style="color: red;">✗ 测试请求失败：' + error + '</span>').show();
                }
            });
        }
    </script>
</head>
<body>
    <form id="form1" runat="server">
        <div class="container">
            <div class="content-bar margin-bottom-bar">
                <table class="infoTable" cellspacing="1" cellpadding="3" border="0" align="center" width="100%">
                    <tr>
                        <td class="left_title_1" width="10%">
                            <span class="red">*</span>
                            <span for="txtServerUrl">服务器地址：</span>
                        </td>
                        <td>
                            <asp:TextBox ID="txtServerUrl" runat="server" placeholder="例如: / 或 ftp://*************" onchange="toggleFtpFields()"></asp:TextBox>
                            <div class="help-text">默认为 "/"，如果使用FTP服务器请输入完整的FTP地址</div>
                        </td>
                    </tr>
                    <tr class="ftp-fields hide">
                        <td class="left_title_1" width="10%">
                            <span class="red">*</span>
                            <span for="txtFtpPort">FTP端口：</span>
                        </td>
                        <td>
                            <asp:TextBox ID="txtFtpPort" runat="server" placeholder="21" Text="21"></asp:TextBox>
                            <div class="help-text">FTP服务器端口号，默认为21</div>
                        </td>
                    </tr>
                    <tr class="ftp-fields hide">
                        <td class="left_title_1" width="10%">
                            <span class="red">*</span>
                            <span for="txtFtpAccount">FTP账号：</span>
                        </td>
                        <td>
                            <asp:TextBox ID="txtFtpAccount" runat="server" placeholder="FTP登录账号"></asp:TextBox>
                        </td>
                    </tr>
                    <tr class="ftp-fields hide">
                        <td class="left_title_1" width="10%">
                            <span class="red">*</span>
                            <span for="txtFtpPassword">FTP密码：</span>
                        </td>
                        <td>
                            <asp:TextBox ID="txtFtpPassword" runat="server" placeholder="FTP登录密码"></asp:TextBox>
                        </td>
                    </tr>
                    <tr class="ftp-fields hide">
                        <td class="left_title_1" width="10%">连接测试：
                        </td>
                        <td>
                            <input type="button" id="btnTestConnection" value="测试FTP连接" class="btn" onclick="testFtpConnection()" />
                            <div id="testResult" style="margin-top: 10px; display: none;"></div>
                        </td>
                    </tr>
                </table>
            </div>
            <div class="bottom-bar">
                <asp:Button ID="btnSave" runat="server" Text="保存配置" CssClass="btnGreen" OnClick="btnSave_Click" OnClientClick="return validateForm();" />
                <input type="button" id="btnCancel" value="关闭" class="btnGray" onclick="CloseBox(); " />
            </div>
        </div>
    </form>

    <script>
        // 页面加载时检查是否需要显示FTP字段
        window.onload = function () {
            toggleFtpFields();
        };
    </script>
</body>
</html>
