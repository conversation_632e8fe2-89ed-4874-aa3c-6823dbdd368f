using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
// 需要安装 FluentFTP NuGet 包
// using FluentFTP;

namespace DMS.Web.dms
{
    public partial class FtpTest : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {

        }

        /// <summary>
        /// 创建FTP连接实例
        /// </summary>
        /// <returns>FTP帮助类实例</returns>
        private FtpHelperFluentFTP CreateFtpHelper()
        {
            string server = txtServer.Text.Trim();
            string username = txtUsername.Text.Trim();
            string password = txtPassword.Text.Trim();
            int port = 21;
            int.TryParse(txtPort.Text.Trim(), out port);

            return new FtpHelperFluentFTP(server, username, password, port);
        }

        /// <summary>
        /// 显示结果信息
        /// </summary>
        /// <param name="resultDiv">结果显示区域</param>
        /// <param name="message">消息内容</param>
        /// <param name="isSuccess">是否成功</param>
        private void ShowResult(System.Web.UI.HtmlControls.HtmlGenericControl resultDiv, string message, bool isSuccess)
        {
            resultDiv.InnerHtml = message;
            resultDiv.Attributes["class"] = isSuccess ? "result success" : "result error";
            resultDiv.Visible = true;
        }

        /// <summary>
        /// 测试FTP连接
        /// </summary>
        protected void btnTestConnection_Click(object sender, EventArgs e)
        {
            try
            {
                using (FtpHelperFluentFTP ftpHelper = CreateFtpHelper())
                {
                    bool connected = ftpHelper.TestConnection();

                    string message = connected ? "FTP连接成功！" : "FTP连接失败，请检查服务器地址、用户名和密码。";
                    ShowResult(divConnectionResult, message, connected);
                }
            }
            catch (Exception ex)
            {
                ShowResult(divConnectionResult, $"连接异常: {FtpHelperFluentFTP.GetDetailedErrorMessage(ex)}", false);
            }
        }

        /// <summary>
        /// 创建目录
        /// </summary>
        protected void btnCreateDirectory_Click(object sender, EventArgs e)
        {
            try
            {
                using (FtpHelperFluentFTP ftpHelper = CreateFtpHelper())
                {
                    string directoryPath = txtDirectoryPath.Text.Trim();

                    bool success = ftpHelper.CreateDirectory(directoryPath);
                    string message = success ? $"目录 '{directoryPath}' 创建成功！" : $"目录 '{directoryPath}' 创建失败。";
                    ShowResult(divDirectoryResult, message, success);
                }
            }
            catch (Exception ex)
            {
                ShowResult(divDirectoryResult, $"创建目录异常: {FtpHelperFluentFTP.GetDetailedErrorMessage(ex)}", false);
            }
        }

        /// <summary>
        /// 删除目录
        /// </summary>
        protected void btnDeleteDirectory_Click(object sender, EventArgs e)
        {
            try
            {
                using (FtpHelperFluentFTP ftpHelper = CreateFtpHelper())
                {
                    string directoryPath = txtDirectoryPath.Text.Trim();

                    bool success = ftpHelper.DeleteDirectory(directoryPath);
                    string message = success ? $"目录 '{directoryPath}' 删除成功！" : $"目录 '{directoryPath}' 删除失败。";
                    ShowResult(divDirectoryResult, message, success);
                }
            }
            catch (Exception ex)
            {
                ShowResult(divDirectoryResult, $"删除目录异常: {FtpHelperFluentFTP.GetDetailedErrorMessage(ex)}", false);
            }
        }

        /// <summary>
        /// 列出目录内容
        /// </summary>
        protected void btnListDirectory_Click(object sender, EventArgs e)
        {
            try
            {
                using (FtpHelperFluentFTP ftpHelper = CreateFtpHelper())
                {
                    string directoryPath = txtDirectoryPath.Text.Trim();

                    // 使用FluentFTP的详细列表方法
                    List<FtpItemInfoFluentFTP> items = ftpHelper.GetDetailedDirectoryList(directoryPath);
                    string message = $"目录 '{directoryPath}' 包含 {items.Count} 个项目:<br/><br/>";

                    message += "<table border='1' style='border-collapse:collapse; width:100%;'>";
                    message += "<tr style='background-color:#f0f0f0;'><th>名称</th><th>类型</th><th>大小</th><th>修改时间</th><th>权限</th></tr>";

                    foreach (FtpItemInfoFluentFTP item in items)
                    {
                        string modifyTime = item.ModifyTime == DateTime.MinValue ? "-" : item.ModifyTime.ToString("yyyy-MM-dd HH:mm:ss");
                        string permissions = string.IsNullOrEmpty(item.Permissions) ? "-" : item.Permissions;
                        message += $"<tr><td>{item.Name}</td><td>{item.TypeDescription}</td><td>{item.FormattedSize}</td><td>{modifyTime}</td><td>{permissions}</td></tr>";
                    }
                    message += "</table>";

                    ShowResult(divDirectoryResult, message, true);
                }
            }
            catch (Exception ex)
            {
                ShowResult(divDirectoryResult, $"列出目录异常: {FtpHelperFluentFTP.GetDetailedErrorMessage(ex)}", false);
            }
        }

        /// <summary>
        /// 检查文件是否存在
        /// </summary>
        protected void btnCheckFileExists_Click(object sender, EventArgs e)
        {
            try
            {
                using (FtpHelperFluentFTP ftpHelper = CreateFtpHelper())
                {
                    string filePath = txtRemoteFilePath.Text.Trim();

                    bool exists = ftpHelper.FileExists(filePath);
                    string message = exists ? $"文件 '{filePath}' 存在。" : $"文件 '{filePath}' 不存在。";
                    ShowResult(divFileResult, message, true);
                }
            }
            catch (Exception ex)
            {
                ShowResult(divFileResult, $"检查文件异常: {FtpHelperFluentFTP.GetDetailedErrorMessage(ex)}", false);
            }
        }

        /// <summary>
        /// 获取文件大小
        /// </summary>
        protected void btnGetFileSize_Click(object sender, EventArgs e)
        {
            try
            {
                using (FtpHelperFluentFTP ftpHelper = CreateFtpHelper())
                {
                    string filePath = txtRemoteFilePath.Text.Trim();

                    long fileSize = ftpHelper.GetFileSize(filePath);
                    string message = fileSize >= 0 ?
                        $"文件 '{filePath}' 大小: {fileSize} 字节 ({fileSize / 1024.0:F2} KB)" :
                        $"无法获取文件 '{filePath}' 的大小。";
                    ShowResult(divFileResult, message, fileSize >= 0);
                }
            }
            catch (Exception ex)
            {
                ShowResult(divFileResult, $"获取文件大小异常: {FtpHelperFluentFTP.GetDetailedErrorMessage(ex)}", false);
            }
        }

        /// <summary>
        /// 删除文件
        /// </summary>
        protected void btnDeleteFile_Click(object sender, EventArgs e)
        {
            try
            {
                using (FtpHelperFluentFTP ftpHelper = CreateFtpHelper())
                {
                    string filePath = txtRemoteFilePath.Text.Trim();

                    bool success = ftpHelper.DeleteFile(filePath);
                    string message = success ? $"文件 '{filePath}' 删除成功！" : $"文件 '{filePath}' 删除失败。";
                    ShowResult(divFileResult, message, success);
                }
            }
            catch (Exception ex)
            {
                ShowResult(divFileResult, $"删除文件异常: {FtpHelperFluentFTP.GetDetailedErrorMessage(ex)}", false);
            }
        }

        /// <summary>
        /// 普通上传文件
        /// </summary>
        protected void btnUpload_Click(object sender, EventArgs e)
        {
            try
            {
                if (!fileUpload.HasFile)
                {
                    ShowResult(divUploadResult, "请选择要上传的文件。", false);
                    return;
                }

                using (FtpHelperFluentFTP ftpHelper = CreateFtpHelper())
                {
                    string remotePath = txtUploadPath.Text.Trim();

                    // 保存上传的文件到临时目录
                    string tempPath = Path.Combine(Server.MapPath("~/App_Data/temp"), fileUpload.FileName);
                    Directory.CreateDirectory(Path.GetDirectoryName(tempPath));
                    fileUpload.SaveAs(tempPath);

                    bool success = ftpHelper.UploadFile(tempPath, remotePath);

                    // 删除临时文件
                    if (File.Exists(tempPath))
                        File.Delete(tempPath);

                    string message = success ? $"文件上传成功到 '{remotePath}'！" : "文件上传失败。";
                    ShowResult(divUploadResult, message, success);
                }
            }
            catch (Exception ex)
            {
                ShowResult(divUploadResult, $"上传文件异常: {FtpHelperFluentFTP.GetDetailedErrorMessage(ex)}", false);
            }
        }

        /// <summary>
        /// 断点续传上传文件
        /// </summary>
        protected void btnUploadResume_Click(object sender, EventArgs e)
        {
            try
            {
                if (!fileUpload.HasFile)
                {
                    ShowResult(divUploadResult, "请选择要上传的文件。", false);
                    return;
                }

                using (FtpHelperFluentFTP ftpHelper = CreateFtpHelper())
                {
                    string remotePath = txtUploadPath.Text.Trim();

                    // 保存上传的文件到临时目录
                    string tempPath = Path.Combine(Server.MapPath("~/App_Data/temp"), fileUpload.FileName);
                    Directory.CreateDirectory(Path.GetDirectoryName(tempPath));
                    fileUpload.SaveAs(tempPath);

                    bool success = ftpHelper.UploadFileWithResume(tempPath, remotePath, 3);

                    // 删除临时文件
                    if (File.Exists(tempPath))
                        File.Delete(tempPath);

                    string message = success ? $"文件断点续传上传成功到 '{remotePath}'！" : "文件断点续传上传失败。";
                    ShowResult(divUploadResult, message, success);
                }
            }
            catch (Exception ex)
            {
                ShowResult(divUploadResult, $"断点续传上传异常: {FtpHelperFluentFTP.GetDetailedErrorMessage(ex)}", false);
            }
        }

        /// <summary>
        /// 流式上传文件
        /// </summary>
        protected void btnUploadStream_Click(object sender, EventArgs e)
        {
            try
            {
                if (!fileUpload.HasFile)
                {
                    ShowResult(divUploadResult, "请选择要上传的文件。", false);
                    return;
                }

                using (FtpHelperFluentFTP ftpHelper = CreateFtpHelper())
                {
                    string remotePath = txtUploadPath.Text.Trim();

                    // 直接从Web控件流式上传到FTP服务器
                    bool success = ftpHelper.UploadFromWebControl(fileUpload, remotePath);

                    string message = success ? $"文件流式上传成功到 '{remotePath}'！" : "文件流式上传失败。";
                    ShowResult(divUploadResult, message, success);
                }
            }
            catch (Exception ex)
            {
                ShowResult(divUploadResult, $"流式上传文件异常: {FtpHelperFluentFTP.GetDetailedErrorMessage(ex)}", false);
            }
        }

        /// <summary>
        /// 普通下载文件
        /// </summary>
        protected void btnDownload_Click(object sender, EventArgs e)
        {
            try
            {
                using (FtpHelperFluentFTP ftpHelper = CreateFtpHelper())
                {
                    string remotePath = txtDownloadRemotePath.Text.Trim();
                    string fileName = Path.GetFileName(remotePath);
                    string localPath = Path.Combine(Server.MapPath("~/App_Data/downloads"), fileName);

                    Directory.CreateDirectory(Path.GetDirectoryName(localPath));

                    bool success = ftpHelper.DownloadFile(remotePath, localPath);

                    string message = success ?
                        $"文件下载成功到 '{localPath}'！<br/>文件大小: {new FileInfo(localPath).Length} 字节" :
                        "文件下载失败。";
                    ShowResult(divDownloadResult, message, success);
                }
            }
            catch (Exception ex)
            {
                ShowResult(divDownloadResult, $"下载文件异常: {FtpHelperFluentFTP.GetDetailedErrorMessage(ex)}", false);
            }
        }

        /// <summary>
        /// 断点续传下载文件
        /// </summary>
        protected void btnDownloadResume_Click(object sender, EventArgs e)
        {
            try
            {
                using (FtpHelperFluentFTP ftpHelper = CreateFtpHelper())
                {
                    string remotePath = txtDownloadRemotePath.Text.Trim();
                    string fileName = Path.GetFileName(remotePath);
                    string localPath = Path.Combine(Server.MapPath("~/App_Data/downloads"), fileName);

                    Directory.CreateDirectory(Path.GetDirectoryName(localPath));

                    bool success = ftpHelper.DownloadFileWithResume(remotePath, localPath, 3);

                    string message = success ?
                        $"文件断点续传下载成功到 '{localPath}'！<br/>文件大小: {new FileInfo(localPath).Length} 字节" :
                        "文件断点续传下载失败。";
                    ShowResult(divDownloadResult, message, success);
                }
            }
            catch (Exception ex)
            {
                ShowResult(divDownloadResult, $"断点续传下载异常: {FtpHelperFluentFTP.GetDetailedErrorMessage(ex)}", false);
            }
        }

        /// <summary>
        /// 获取系统信息
        /// </summary>
        protected void btnGetSystemInfo_Click(object sender, EventArgs e)
        {
            try
            {
                using (FtpHelperFluentFTP ftpHelper = CreateFtpHelper())
                {
                    string systemType = ftpHelper.GetSystemType();
                    string currentDir = ftpHelper.GetCurrentDirectory();

                    string message = $"FTP服务器系统类型: {systemType}<br/>当前工作目录: {currentDir}";
                    ShowResult(divSystemResult, message, true);
                }
            }
            catch (Exception ex)
            {
                ShowResult(divSystemResult, $"获取系统信息异常: {FtpHelperFluentFTP.GetDetailedErrorMessage(ex)}", false);
            }
        }

        /// <summary>
        /// 测试异步操作
        /// </summary>
        protected async void btnTestAsync_Click(object sender, EventArgs e)
        {
            try
            {
                using (FtpHelperFluentFTP ftpHelper = CreateFtpHelper())
                {
                    // 异步连接测试
                    bool connected = await ftpHelper.TestConnectionAsync();
                    string message = $"异步连接测试: {(connected ? "成功" : "失败")}<br/>";

                    if (connected)
                    {
                        // 异步获取系统信息
                        string systemType = await ftpHelper.GetSystemTypeAsync();
                        string currentDir = await ftpHelper.GetCurrentDirectoryAsync();

                        message += $"异步获取系统类型: {systemType}<br/>";
                        message += $"异步获取当前目录: {currentDir}<br/>";

                        // 异步获取目录列表
                        List<string> directories = await ftpHelper.GetDirectoryListAsync("/");
                        message += $"异步获取目录列表: {directories.Count} 个目录<br/>";

                        // 异步获取文件列表
                        List<string> files = await ftpHelper.GetFileListAsync("/");
                        message += $"异步获取文件列表: {files.Count} 个文件";
                    }

                    ShowResult(divSystemResult, message, connected);
                }
            }
            catch (Exception ex)
            {
                ShowResult(divSystemResult, $"异步操作异常: {FtpHelperFluentFTP.GetDetailedErrorMessage(ex)}", false);
            }
        }

        /// <summary>
        /// 批量上传测试
        /// </summary>
        protected void btnBatchUpload_Click(object sender, EventArgs e)
        {
            try
            {
                using (FtpHelperFluentFTP ftpHelper = CreateFtpHelper())
                {
                    string batchDir = txtBatchDirectory.Text.Trim();

                    // 创建测试文件
                    string tempDir = Server.MapPath("~/App_Data/batch_test");
                    Directory.CreateDirectory(tempDir);

                    List<string> testFiles = new List<string>();
                    for (int i = 1; i <= 3; i++)
                    {
                        string testFile = Path.Combine(tempDir, $"batch_test_{i}.txt");
                        File.WriteAllText(testFile, $"FluentFTP 批量测试文件 {i} - {DateTime.Now}");
                        testFiles.Add(testFile);
                    }

                    // 创建远程目录
                    ftpHelper.CreateDirectory(batchDir);

                    // 批量上传
                    int uploadedCount = ftpHelper.BatchUploadFiles(testFiles, batchDir, true);

                    // 清理临时文件
                    foreach (string file in testFiles)
                    {
                        if (File.Exists(file))
                            File.Delete(file);
                    }

                    string message = $"批量上传测试完成！<br/>";
                    message += $"成功上传 {uploadedCount} 个文件到 '{batchDir}'<br/>";
                    message += $"使用了断点续传功能";

                    ShowResult(divFluentFtpResult, message, uploadedCount > 0);
                }
            }
            catch (Exception ex)
            {
                ShowResult(divFluentFtpResult, $"批量上传异常: {FtpHelperFluentFTP.GetDetailedErrorMessage(ex)}", false);
            }
        }

         
    }
}
