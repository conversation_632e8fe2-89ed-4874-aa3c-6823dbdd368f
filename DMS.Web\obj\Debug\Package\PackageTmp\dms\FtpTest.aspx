﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="FtpTest.aspx.cs" Inherits="DMS.Web.dms.FtpTest" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>FTP功能测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .section { margin-bottom: 30px; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .section h3 { margin-top: 0; color: #333; }
        .form-group { margin-bottom: 15px; }
        .form-group label { display: inline-block; width: 120px; font-weight: bold; }
        .form-group input, .form-group select { width: 200px; padding: 5px; }
        .btn { padding: 8px 15px; margin: 5px; background-color: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer; }
        .btn:hover { background-color: #005a87; }
        .result { margin-top: 10px; padding: 10px; background-color: #f9f9f9; border-left: 4px solid #007cba; }
        .error { border-left-color: #d32f2f; background-color: #ffebee; }
        .success { border-left-color: #388e3c; background-color: #e8f5e8; }
    </style>
</head>
<body>
    <form id="form1" runat="server">
        <div class="container">
            <h1>FTP功能测试页面</h1>

            <!-- FTP连接配置 -->
            <div class="section">
                <h3>FTP连接配置</h3>
                <div class="form-group">
                    <label>服务器地址:</label>
                    <asp:TextBox ID="txtServer" runat="server" Text="192.168.1.8"></asp:TextBox>
                </div>
                <div class="form-group">
                    <label>用户名:</label>
                    <asp:TextBox ID="txtUsername" runat="server" Text="xueyun"></asp:TextBox>
                </div>
                <div class="form-group">
                    <label>密码:</label>
                    <asp:TextBox ID="txtPassword" runat="server" Text="xueyun"></asp:TextBox>
                </div>
                <div class="form-group">
                    <label>端口:</label>
                    <asp:TextBox ID="txtPort" runat="server" Text="21"></asp:TextBox>
                </div>
                <asp:Button ID="btnTestConnection" runat="server" Text="测试连接" CssClass="btn" OnClick="btnTestConnection_Click" />
                <div id="divConnectionResult" runat="server" class="result" visible="false"></div>
            </div>

            <!-- 目录操作 -->
            <div class="section">
                <h3>目录操作</h3>
                <div class="form-group">
                    <label>目录路径:</label>
                    <asp:TextBox ID="txtDirectoryPath" runat="server" Text="/test_folder"></asp:TextBox>
                </div>
                <asp:Button ID="btnCreateDirectory" runat="server" Text="创建目录" CssClass="btn" OnClick="btnCreateDirectory_Click" />
                <asp:Button ID="btnDeleteDirectory" runat="server" Text="删除目录" CssClass="btn" OnClick="btnDeleteDirectory_Click" />
                <asp:Button ID="btnListDirectory" runat="server" Text="列出目录" CssClass="btn" OnClick="btnListDirectory_Click" />
                <div id="divDirectoryResult" runat="server" class="result" visible="false"></div>
            </div>

            <!-- 文件操作 -->
            <div class="section">
                <h3>文件操作</h3>
                <div class="form-group">
                    <label>远程文件路径:</label>
                    <asp:TextBox ID="txtRemoteFilePath" runat="server" Text="/test_file.txt"></asp:TextBox>
                </div>
                <asp:Button ID="btnCheckFileExists" runat="server" Text="检查文件存在" CssClass="btn" OnClick="btnCheckFileExists_Click" />
                <asp:Button ID="btnGetFileSize" runat="server" Text="获取文件大小" CssClass="btn" OnClick="btnGetFileSize_Click" />
                <asp:Button ID="btnDeleteFile" runat="server" Text="删除文件" CssClass="btn" OnClick="btnDeleteFile_Click" />
                <div id="divFileResult" runat="server" class="result" visible="false"></div>
            </div>

            <!-- 文件上传 -->
            <div class="section">
                <h3>文件上传</h3>
                <div class="form-group">
                    <label>选择文件:</label>
                    <asp:FileUpload ID="fileUpload" runat="server" />
                </div>
                <div class="form-group">
                    <label>远程路径:</label>
                    <asp:TextBox ID="txtUploadPath" runat="server" Text="/uploaded_file.txt"></asp:TextBox>
                </div>
                <asp:Button ID="btnUpload" runat="server" Text="普通上传" CssClass="btn" OnClick="btnUpload_Click" />
                <asp:Button ID="btnUploadResume" runat="server" Text="断点续传上传" CssClass="btn" OnClick="btnUploadResume_Click" />
                <asp:Button ID="btnUploadStream" runat="server" Text="流式上传" CssClass="btn" OnClick="btnUploadStream_Click" />
                <div id="divUploadResult" runat="server" class="result" visible="false"></div>
            </div>

            <!-- 文件下载 -->
            <div class="section">
                <h3>文件下载</h3>
                <div class="form-group">
                    <label>远程文件:</label>
                    <asp:TextBox ID="txtDownloadRemotePath" runat="server" Text="/uploaded_file.txt"></asp:TextBox>
                </div>
                <asp:Button ID="btnDownload" runat="server" Text="普通下载" CssClass="btn" OnClick="btnDownload_Click" />
                <asp:Button ID="btnDownloadResume" runat="server" Text="断点续传下载" CssClass="btn" OnClick="btnDownloadResume_Click" />
                <div id="divDownloadResult" runat="server" class="result" visible="false"></div>
            </div>

            <!-- 系统信息 -->
            <div class="section">
                <h3>系统信息</h3>
                <asp:Button ID="btnGetSystemInfo" runat="server" Text="获取系统信息" CssClass="btn" OnClick="btnGetSystemInfo_Click" />
                <asp:Button ID="btnTestAsync" runat="server" Text="测试异步操作" CssClass="btn" OnClick="btnTestAsync_Click" />
                <div id="divSystemResult" runat="server" class="result" visible="false"></div>
            </div>

            <!-- FluentFTP 特性展示 -->
            <div class="section">
                <h3>FluentFTP 特性展示</h3>
                <div class="form-group">
                    <label>批量操作目录:</label>
                    <asp:TextBox ID="txtBatchDirectory" runat="server" Text="/batch_test"></asp:TextBox>
                </div>
                <asp:Button ID="btnBatchUpload" runat="server" Text="批量上传测试" CssClass="btn" OnClick="btnBatchUpload_Click" />
                <div id="divFluentFtpResult" runat="server" class="result" visible="false"></div>
            </div>
        </div>
    </form>
</body>
</html>
