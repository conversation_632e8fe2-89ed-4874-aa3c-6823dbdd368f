﻿using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Linq;
using System.Web;
using System.Web.Security;
using YunEdu.Common.DEncrypt;

namespace DMS.Web
{
    public class BasePage : System.Web.UI.Page
    {
        /// <summary>
        /// sso url
        /// </summary>
        string ssoUrl = "";

        /// <summary>
        /// sso appKey
        /// </summary>
        string appKey = "";

        /// <summary>
        /// sso appSecret
        /// </summary>
        string appSecret = "";

        bool _IsEnableWelcome = true; //是否启用引导页
        string _UseVersion = "4.0"; //系统使用的版本

        private YunEdu.BLL.BM_Areas bllArea = new YunEdu.BLL.BM_Areas();

        public BasePage()
        {
            ssoUrl = ConfigurationManager.AppSettings["SSOUrl"];
            appKey = ConfigurationManager.AppSettings["AppKey"];
            appSecret = ConfigurationManager.AppSettings["AppSecret"];
        }

        protected override void OnPreInit(EventArgs e)
        {
            // 没有登录
            if (!User.Identity.IsAuthenticated)
            {
                bool isLogin = HandleSSOLogin();
                if (!isLogin)
                {
                    RedirectToSSOLogin();
                }
            }
        }

        /// <summary>
        /// 角色等级
        /// </summary>
        public string[] RoleLevel
        {
            get
            {
                if (HttpContext.Current.Request.Cookies["RoleLevel"] != null)
                    return DESEncrypt.Decrypt(HttpContext.Current.Request.Cookies["RoleLevel"].Value).Split(',');
                else
                    throw new Exception("登录超时，请重新登录！");
            }
        }

        private MembershipUser _users = null;
        /// <summary>
        /// 获取用户对象MembershipUser
        /// </summary>
        public MembershipUser MyUser
        {
            get
            {
                if (_users != null)
                {
                    return _users;
                }
                else
                {
                    if (User.Identity.IsAuthenticated)
                    {
                        _users = Membership.GetUser();
                        return _users;
                    }
                    else
                    {
                        #region 没有登录或User对象还未启作用
                        if (HttpContext.Current.Session["UserId"] != null)
                        {
                            if (HttpContext.Current.Session["UserId"].ToString().Equals(""))
                            {
                                return null;
                            }
                            else
                            {
                                _users = Membership.GetUser(new Guid(HttpContext.Current.Session["UserId"].ToString()));
                                return _users;
                            }
                        }
                        else
                        {
                            if (HttpContext.Current.Request.Cookies["UserId"] != null)
                            {
                                if (HttpContext.Current.Request.Cookies["UserId"].Value.ToString().Equals(""))
                                {
                                    return null;
                                }
                                else
                                {
                                    _users = Membership.GetUser(DESEncrypt.Decrypt(HttpContext.Current.Request.Cookies["UserId"].Value));
                                    return _users;
                                }
                            }
                            else
                            {
                                return null;
                            }
                        }
                        #endregion
                    }
                }
            }
        }

        /// <summary>
        /// 得到当前用户ID
        /// </summary>
        public string UserId
        {
            get
            {
                if (MyUser != null)
                    return MyUser.ProviderUserKey.ToString();
                else
                    return Guid.Empty.ToString();
            }
        }

        #region 获得当前用户UserName
        /// <summary>
        /// 得到当前用户ID
        /// </summary>
        public string UserName
        {
            get
            {
                if (MyUser != null)
                    return MyUser.UserName;
                else
                    return "";
            }
        }
        #endregion

        private YunEdu.Model.BM_Areas _modelAreaUser = null;
        /// <summary>
        /// 获取用户BM_Areas所属辖区对象
        /// </summary>
        public YunEdu.Model.BM_Areas modelAreaUser
        {
            get
            {
                if (_modelAreaUser != null)
                {
                    return _modelAreaUser;
                }
                else
                {
                    if (User.Identity.IsAuthenticated)
                    {
                        DataSet dsAreaInfo = bllArea.GetAreaInfo(new Guid(MyUser.ProviderUserKey.ToString()));
                        if (dsAreaInfo != null && dsAreaInfo.Tables.Count > 0 && dsAreaInfo.Tables[0].Rows.Count > 0)
                        {
                            _modelAreaUser = bllArea.DataTableToList(dsAreaInfo.Tables[0])[0];
                        }
                        if (_modelAreaUser != null)
                        {
                            return _modelAreaUser;
                        }
                        else
                        {
                            throw new Exception("信息不存在！");
                        }
                    }
                    else
                    {
                        throw new Exception("登录超时，请重新登录！");
                    }
                }
            }
            set
            {
                _modelAreaUser = value;
            }
        }

        /// <summary>
        /// 处理sso登录
        /// </summary>
        private bool HandleSSOLogin()
        {
            string token = Request.QueryString["token"];
            // 没有token
            if (string.IsNullOrEmpty(token))
            {
                return false;
            }

            // 验证token
            string url = $"{ssoUrl}/api/validate";
            YunEdu.Common.RequestHelper requestHelper = new YunEdu.Common.RequestHelper();
            string postData = $"appKey={appKey}&appSecret={appSecret}&token={token}";
            string res = requestHelper.RequestWithPost(url, postData);
            if (!string.IsNullOrEmpty(res))
            {
                var obj = Newtonsoft.Json.JsonConvert.DeserializeObject<SSOLoginInfo>(res);
                if (obj != null && string.IsNullOrEmpty(obj.ErrorMsg))
                {
                    return AutoLogin(obj.User.UserID, token);
                }
            }
            return false;
        }

        /// <summary>
        /// 自动登录
        /// </summary>
        /// <returns></returns>
        private bool AutoLogin(Guid userId, string token)
        {
            YunEdu.BLL.aspnet_Membership bll_membership = new YunEdu.BLL.aspnet_Membership();
            bool _success = false;
            try
            {
                // 获取用户信息
                YunEdu.Model.aspnet_Membership model_membership = bll_membership.GetModel(userId);
                if (model_membership == null)
                {
                    return false;
                }

                //清除Session
                Session["AuthorityStr"] = null;
                Session.Clear();

                //获取子系统AppId
                YunEdu.BLL.aspnet_Applications bll_app = new YunEdu.BLL.aspnet_Applications();
                Guid _appId = bll_app.GetApplicationId();



                #region 设置COOKIE
                DateTime dt = new DateTime();
                if (Request.Form["chkRemeber"] == "true")
                {
                    dt = DateTime.Now.AddDays(14);//2周内免登录
                }
                else
                {
                    dt = DateTime.Now.AddHours(20);//默认20小时
                }

                //定义票据
                FormsAuthenticationTicket ticket = new FormsAuthenticationTicket(1, model_membership.UserName, DateTime.Now, dt.AddMinutes(-10), true, Request.UserHostAddress, FormsAuthentication.FormsCookiePath);
                //把验证票加密
                string ticketEncrypted = FormsAuthentication.Encrypt(ticket);
                //设置验证票cookie,第一个参数为cookie的名字,第二个参数为cookie的值也就是加密后的票
                System.Web.HttpCookie myCookie = new System.Web.HttpCookie(FormsAuthentication.FormsCookieName, ticketEncrypted);
                //不能通过客户端脚本访问
                myCookie.HttpOnly = false;
                myCookie.Path = FormsAuthentication.FormsCookiePath;
                //myCookie.Secure = FormsAuthentication.RequireSSL;
                myCookie.Expires = dt;
                //把cookie加进Response对象发生到客户端
                Response.Cookies.Add(myCookie);


                //是否启用引导页
                System.Web.HttpCookie myIsEnableWelcome = new System.Web.HttpCookie("IsEnableWelcome");
                myIsEnableWelcome.Value = _IsEnableWelcome.ToString();
                myIsEnableWelcome.Expires = dt;
                Response.Cookies.Add(myIsEnableWelcome);

                //系统使用版本
                System.Web.HttpCookie myUseVersion = new System.Web.HttpCookie("UseVersion");
                myUseVersion.Value = _UseVersion.ToString();
                myUseVersion.Expires = dt;
                Response.Cookies.Add(myUseVersion);

                //用户ID
                System.Web.HttpCookie myUserId = new System.Web.HttpCookie("UserId");
                myUserId.Value = DESEncrypt.Encrypt(userId.ToString());
                myUserId.Expires = dt;
                Response.Cookies.Add(myUserId);

                //用户名
                System.Web.HttpCookie myUserName = new System.Web.HttpCookie("UserName");
                myUserName.Value = DESEncrypt.Encrypt(model_membership.UserName);
                myUserName.Expires = dt;
                Response.Cookies.Add(myUserName);

                //用户中文名
                string _userCName = model_membership.CName;
                if (string.IsNullOrEmpty(_userCName))
                {
                    //找不到中文名默认为用户名
                    _userCName = model_membership.UserName;
                }

                System.Web.HttpCookie myCName = new System.Web.HttpCookie("UserCName");
                myCName.Value = DESEncrypt.Encrypt(_userCName);
                myCName.Expires = dt;
                Response.Cookies.Add(myCName);

                //用户照片
                string _UserPhoto = "/admin/images/photo_default.png";
                if (string.IsNullOrWhiteSpace(model_membership.Photo))
                    _UserPhoto = model_membership.Photo;

                System.Web.HttpCookie myPhoto = new System.Web.HttpCookie("UserPhoto");
                myPhoto.Value = DESEncrypt.Encrypt(_UserPhoto);
                myPhoto.Expires = dt;
                Response.Cookies.Add(myPhoto);

                //把用户权限保存到cookie
                string _AuthorityStr = "";
                YunEdu.BLL.aspnet_RolesAuthority bll_RoleAuthority = new YunEdu.BLL.aspnet_RolesAuthority();
                YunEdu.BLL.aspnet_UsersAuthority bll_UserAuthority = new YunEdu.BLL.aspnet_UsersAuthority();

                //3.0
                //_AuthorityStr = bll_RoleAuthority.GetRoleAuthorityByUser(_appId,_userGuid, model_membership.ColumnPath);
                //4.0
                _AuthorityStr = bll_RoleAuthority.GetRoleAuthorityByUser(userId, model_membership.ColumnPath, 3);
                //权限信息 可能太长会超过cookie的限制
                Session["AuthorityStr"] = DESEncrypt.Encrypt(_AuthorityStr);


                //用户角色名，规定：管员身份只能有其中一个。但可以同时为教师身份
                System.Web.HttpCookie myRoleName = new System.Web.HttpCookie("RolesName");
                string[] _roles = Roles.GetRolesForUser(model_membership.UserName);
                if (_roles == null || _roles.Length == 0)
                    throw new Exception("当前用户没有配置角色！");

                string _role = "";
                foreach (string s in _roles)
                {
                    _role += s + ",";
                }
                myRoleName.Value = DESEncrypt.Encrypt(_role);
                myRoleName.Expires = dt;
                Response.Cookies.Add(myRoleName);

                //角色描述
                string _roleDescrition = "";
                YunEdu.BLL.aspnet_Roles bll_role = new YunEdu.BLL.aspnet_Roles();
                _roleDescrition = bll_role.GetRoleDesciption(userId);
                if (string.IsNullOrEmpty(_roleDescrition))
                    throw new Exception("当前用户没有配置角色！");

                System.Web.HttpCookie myRoleDescrition = new System.Web.HttpCookie("RolesDescription");
                myRoleDescrition.Value = DESEncrypt.Encrypt(_roleDescrition);
                myRoleDescrition.Expires = dt;
                Response.Cookies.Add(myRoleDescrition);

                //角色等级
                string _roleLevel = "";
                _roleLevel = bll_role.GetRoleLevel(userId);
                if (_roleLevel == null)
                    _roleLevel = "";

                System.Web.HttpCookie myRoleLevel = new System.Web.HttpCookie("RoleLevel");
                myRoleLevel.Value = DESEncrypt.Encrypt(_roleLevel);
                myRoleLevel.Expires = dt;
                Response.Cookies.Add(myRoleLevel);

                //家长角色需要判断小孩数
                if (_roles.Contains("Parents"))
                {
                    YunEdu.BLL.JC_StudentFamily bll = new YunEdu.BLL.JC_StudentFamily();
                    DataSet ds = bll.GetChilds(userId);

                    //只有一个小孩时，保存StudentId
                    //有至少2小孩的家长，登录过程暂时不保存StudentId
                    if (ds != null && ds.Tables[0].Rows.Count == 1)
                    {
                        System.Web.HttpCookie myStudentId = new System.Web.HttpCookie("StudentId");
                        myStudentId.Value = DESEncrypt.Encrypt(ds.Tables[0].Rows[0]["StudentId"].ToString());
                        myStudentId.Expires = dt;
                        Response.Cookies.Add(myStudentId);
                    }
                }
                #endregion 

                #region SSO
                string _token = YunEdu.SSO.AppConstants.GetGuidHash();  //票据值
                string _server = "1";

                System.Web.HttpCookie tokenCookie = new System.Web.HttpCookie("tokenCookie", _token);
                tokenCookie.HttpOnly = false;
                tokenCookie.Expires = dt;
                Response.Cookies.Add(tokenCookie);

                System.Web.HttpCookie refreshTime = new System.Web.HttpCookie("refreshTime");
                tokenCookie.HttpOnly = false;
                refreshTime.Value = DateTime.Now.ToString();
                refreshTime.Expires = dt;
                Response.Cookies.Add(refreshTime);

                //写入用户在线表
                MembershipUser mu = Membership.GetUser(model_membership.UserName);
                string[] roleName = Roles.GetRolesForUser(model_membership.UserName);
                string _roleName = "";
                if (roleName.Length > 0)
                    _roleName = roleName[0].ToString();

                DataSet areaDs = bllArea.GetAreaInfo(new Guid(mu.ProviderUserKey.ToString()));
                int columnID = 0; string columnPath = "";
                if (areaDs.Tables[0].Rows.Count > 0)
                {
                    int.TryParse(areaDs.Tables[0].Rows[0]["ColumnID"].ToString(), out columnID);
                    columnPath = areaDs.Tables[0].Rows[0]["ColumnPath"].ToString();
                }

                YunEdu.SSO.LoginApp.SetUserOnline(mu.ProviderUserKey.ToString(), model_membership.UserName, _token, YunEdu.SSO.LoginApp.GetUserFrom(Request.QueryString["ReturnUrl"]),
                    _roleName, mu.Email, columnID, columnPath, HttpContext.Current, _server);
                #endregion
                Session["authentication"] = token;
                _success = true;
            }
            catch (Exception)
            {
                //发生错误时，进行注销操作 
                FormsAuthentication.SignOut();
                //清除cookie
                System.Web.HttpCookie aCookie;
                string cookieName;
                int limit = Request.Cookies.Count;
                for (int i = 0; i < limit; i++)
                {
                    cookieName = Request.Cookies[i].Name;
                    aCookie = new System.Web.HttpCookie(cookieName);
                    aCookie.Value = "";
                    aCookie.Expires = DateTime.Now.AddDays(-1);
                    Response.Cookies.Add(aCookie);
                }
                //清除Session
                Session["AuthorityStr"] = null;
                Session.Clear();
                Session.Abandon();
            }
            return _success;
        }

        /// <summary>
        /// 重定向到SSO登录页面
        /// </summary>
        private void RedirectToSSOLogin()
        {
            // 当前访问的url
            string currentUrl = Request.Url.AbsoluteUri;
            string url = $"{ssoUrl}/login.aspx?ReturnUrl={currentUrl}";
            Response.Redirect(url);
        }
    }

    /// <summary>
    /// SSO登录信息
    /// </summary>
    public class SSOLoginInfo
    {
        /// <summary>
        /// 错误信息
        /// </summary>
        public string ErrorMsg { get; set; }

        /// <summary>
        /// 登录用户信息
        /// </summary>
        public SSOLoginUser User { get; set; }
    }

    /// <summary>
    /// SSO登录用户信息
    /// </summary>
    public class SSOLoginUser
    {
        /// <summary>
        /// 用户Id
        /// </summary>
        public Guid UserID { get; set; }
    }
}