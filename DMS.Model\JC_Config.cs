﻿using System;
namespace DMS.Model
{
	/// <summary>
	/// 配置信息表
	/// </summary>
	[Serializable]
	public partial class JC_Config
	{
		public JC_Config()
		{}
		#region Model
		private Guid _id;
		private int _areaid;
		private string _domainname;
		private bool _isenabled;
		private string _registercode;
		private string _indate;
		private bool _isenabledmodifypwd;
		private string _areapath;
		private string _systemname;
		private bool _isenablewelcome;
		private string _useversion;
		private string _authresult;
		private string _apiurl;
		private string _schoolids;
		private int? _facemodule;
		private string _hkface;
		private string _baiduface;
		private int? _uploadfilesizelimit;
		private string _uploadfiletypelimit;
		/// <summary>
		/// 
		/// </summary>
		public Guid ID
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 机构地区编号。对应信息系统学校ColumnId
		/// </summary>
		public int AreaId
		{
			set{ _areaid=value;}
			get{return _areaid;}
		}
		/// <summary>
		/// 域名
		/// </summary>
		public string DomainName
		{
			set{ _domainname=value;}
			get{return _domainname;}
		}
		/// <summary>
		/// 启用
		/// </summary>
		public bool IsEnabled
		{
			set{ _isenabled=value;}
			get{return _isenabled;}
		}
		/// <summary>
		/// 注册码
		/// </summary>
		public string RegisterCode
		{
			set{ _registercode=value;}
			get{return _registercode;}
		}
		/// <summary>
		/// 有效使用时间。如：2018-07-31
		/// </summary>
		public string Indate
		{
			set{ _indate=value;}
			get{return _indate;}
		}
		/// <summary>
		/// 
		/// </summary>
		public bool IsEnabledModifyPwd
		{
			set{ _isenabledmodifypwd=value;}
			get{return _isenabledmodifypwd;}
		}
		/// <summary>
		/// 区域路径
		/// </summary>
		public string AreaPath
		{
			set{ _areapath=value;}
			get{return _areapath;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string SystemName
		{
			set{ _systemname=value;}
			get{return _systemname;}
		}
		/// <summary>
		/// 
		/// </summary>
		public bool IsEnableWelcome
		{
			set{ _isenablewelcome=value;}
			get{return _isenablewelcome;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string UseVersion
		{
			set{ _useversion=value;}
			get{return _useversion;}
		}
		/// <summary>
		/// 认证结果
		/// </summary>
		public string AuthResult
		{
			set{ _authresult=value;}
			get{return _authresult;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string ApiUrl
		{
			set{ _apiurl=value;}
			get{return _apiurl;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string SchoolIds
		{
			set{ _schoolids=value;}
			get{return _schoolids;}
		}
		/// <summary>
		/// 启用的人脸模块
		/// </summary>
		public int? FaceModule
		{
			set{ _facemodule=value;}
			get{return _facemodule;}
		}
		/// <summary>
		/// 海康人脸配置
		/// </summary>
		public string HKFace
		{
			set{ _hkface=value;}
			get{return _hkface;}
		}
		/// <summary>
		/// 百度人脸配置
		/// </summary>
		public string BaiduFace
		{
			set{ _baiduface=value;}
			get{return _baiduface;}
		}
		/// <summary>
		/// 
		/// </summary>
		public int? UploadFileSizeLimit
		{
			set{ _uploadfilesizelimit=value;}
			get{return _uploadfilesizelimit;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string UploadFileTypeLimit
		{
			set{ _uploadfiletypelimit=value;}
			get{return _uploadfiletypelimit;}
		}
		#endregion Model

	}
}

