-- 为dms_files表添加MD5和文件状态字段
-- 执行前请备份数据库

USE [YourDatabaseName]  -- 请替换为实际的数据库名
GO

-- 检查字段是否已存在，如果不存在则添加
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[dms_files]') AND name = 'FileStatus')
BEGIN
    ALTER TABLE [dbo].[dms_files] ADD [FileStatus] [int] NOT NULL DEFAULT(2)
    PRINT 'FileStatus字段添加成功'
END
ELSE
BEGIN
    PRINT 'FileStatus字段已存在'
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[dms_files]') AND name = 'FileMD5')
BEGIN
    ALTER TABLE [dbo].[dms_files] ADD [FileMD5] [nvarchar](50) NULL
    PRINT 'FileMD5字段添加成功'
END
ELSE
BEGIN
    PRINT 'FileMD5字段已存在'
END

-- 为FileMD5字段创建索引以提高查询性能
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[dms_files]') AND name = 'IX_dms_files_FileMD5')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_dms_files_FileMD5] ON [dbo].[dms_files]
    (
        [FileMD5] ASC
    )
    WHERE ([FileMD5] IS NOT NULL)
    PRINT 'FileMD5索引创建成功'
END
ELSE
BEGIN
    PRINT 'FileMD5索引已存在'
END

-- 更新现有记录的FileStatus为已完成状态（2）
UPDATE [dbo].[dms_files] SET [FileStatus] = 2 WHERE [FileStatus] IS NULL OR [FileStatus] = 0
PRINT '现有文件状态更新完成'

-- 查看表结构
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'dms_files' 
    AND COLUMN_NAME IN ('FileStatus', 'FileMD5')
ORDER BY ORDINAL_POSITION

PRINT '字段添加完成！'
PRINT '字段说明：'
PRINT 'FileStatus: 1=上传中, 2=上传完成'
PRINT 'FileMD5: 文件的MD5值，用于重复文件检测和断点续传'
