﻿using System;
using YunEdu.Common;

namespace DMS.Web.dms
{
    public partial class dms_config_edit : BasePage
    {
        private DMS.BLL.dms_config bllConfig = new DMS.BLL.dms_config();

        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                LoadConfigData();
            }
        }

        /// <summary>
        /// 加载配置数据
        /// </summary>
        private void LoadConfigData()
        {
            try
            {
                // 根据地区ID获取配置信息
                DMS.Model.dms_config model = bllConfig.GetModelByColumnId(modelAreaUser.ColumnID);

                if (model != null)
                {
                    // 编辑模式，加载现有数据
                    txtServerUrl.Text = model.ServerUrl ?? "/";

                    if (IsFtpUrl(model.ServerUrl))
                    {
                        txtFtpPort.Text = model.FtpPort.ToString();
                        txtFtpAccount.Text = model.FtpAccount ?? "";
                        txtFtpPassword.Text = model.FtpPassword ?? "";
                    }
                }
                else
                {
                    // 新增模式，设置默认值
                    txtServerUrl.Text = "/";
                    txtFtpPort.Text = "21";
                }
            }
            catch (Exception)
            {
                MessageBox.ResponseScript(this, "layer.msg('加载配置数据失败!');setTimeout(function(){CloseBox();},800)");
            }
        }

        /// <summary>
        /// 验证输入
        /// </summary>
        /// <returns>验证是否通过</returns>
        private bool ValidateInput()
        {
            string strServerUrl = txtServerUrl.Text.Trim();

            if (string.IsNullOrEmpty(strServerUrl))
            {
                MessageBox.ResponseScript(this, "layer.msg('请输入服务器地址!');");
                return false;
            }

            // 如果是FTP地址，验证FTP相关信息
            if (IsFtpUrl(strServerUrl))
            {
                string strFtpPort = txtFtpPort.Text.Trim();
                string strFtpAccount = txtFtpAccount.Text.Trim();
                string strFtpPassword = txtFtpPassword.Text.Trim();

                if (string.IsNullOrEmpty(strFtpPort) || string.IsNullOrEmpty(strFtpAccount) || string.IsNullOrEmpty(strFtpPassword))
                {
                    MessageBox.ResponseScript(this, "layer.msg('FTP地址需要填写端口、账号和密码信息!');");
                    return false;
                }

                int nPort;
                if (!int.TryParse(strFtpPort, out nPort) || nPort < 1 || nPort > 65535)
                {
                    MessageBox.ResponseScript(this, "layer.msg('请输入有效的端口号(1-65535)!');");
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// 判断是否为FTP地址
        /// </summary>
        /// <param name="url">URL地址</param>
        /// <returns>是否为FTP地址</returns>
        private bool IsFtpUrl(string url)
        {
            if (string.IsNullOrEmpty(url))
                return false;

            return url.ToLower().StartsWith("ftp://") || url.ToLower().Contains("ftp");
        }

        /// <summary>
        /// 保存配置按钮点击事件
        /// </summary>
        protected void btnSave_Click(object sender, EventArgs e)
        {
            if (!ValidateInput())
                return;

            string strServerUrl = txtServerUrl.Text.Trim();

            // 检查是否已存在配置
            bool isExists = false;
            DMS.Model.dms_config model = bllConfig.GetModelByColumnId(modelAreaUser.ColumnID);
            if (model == null)
            {
                model = new DMS.Model.dms_config();
            }
            else
            {
                isExists = true;
            }

            // 设置基本信息
            model.ColumnId = modelAreaUser.ColumnID;
            model.ColumnPath = modelAreaUser.ColumnPath;
            model.ServerUrl = strServerUrl;
            model.LastEditBy = new Guid(UserId);
            model.LastEditTime = DateTime.Now;

            // 如果是FTP地址，设置FTP相关信息
            if (IsFtpUrl(strServerUrl))
            {
                int nPort;
                if (int.TryParse(txtFtpPort.Text.Trim(), out nPort))
                {
                    model.FtpPort = nPort;
                }
                else
                {
                    model.FtpPort = 21; // 默认端口
                }

                model.FtpAccount = txtFtpAccount.Text.Trim();
                model.FtpPassword = txtFtpPassword.Text.Trim();
            }
            else
            {
                // 非FTP地址，清空FTP相关信息
                model.FtpPort = 0;
                model.FtpAccount = "";
                model.FtpPassword = "";
            }

            bool isSuccess = isExists ? bllConfig.Update(model) : bllConfig.Add(model);
            string scripts = isSuccess ? "layer.msg('配置保存成功!');setTimeout(function(){CloseBox(1);},800)" : "layer.msg('配置保存失败，请重试!')";
            MessageBox.ResponseScript(this, scripts);
        }
    }
}