﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="dms_file_Add.aspx.cs" Inherits="DMS.Web.dms.dms_file_Add"
    ValidateRequest="false" StylesheetTheme="Admin_Default" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">

<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>上传文件</title>
    <link href="/js/webuploader/webuploader.css" rel="stylesheet" />
    <link href="css/dms_file_Add.css" rel="stylesheet" />
    <script src="/js/jquery-1.8.3.min.js" type="text/javascript"></script>
    <script src="/js/layer/layer.js" type="text/javascript"></script>
    <script src="/admin/js/Common.js" type="text/javascript"></script>
    <script src="/js/webuploader/webuploader.js" type="text/javascript"></script>
    <script src="js/webuploader.helper.file.js"></script>
    <style>
        .upload-list__progress__inner { transition: width .3s cubic-bezier(0.47, 1.19, 0.25, 1); }
    </style>
    <script>
        function CloseBox() {
            parent.layer.close(parent.layer.getFrameIndex(window.name));
        };
        function showMessage(success, content, ico) {
            var _index = parent.layer.alert(content, {
                icon: ico,
                shade: ['0.1', '#000'],
                time: success == "true" ? 1000 : 0,
                end: function () {
                    if (success == "true") {
                        parent.refreshData();
                        CloseBox();
                    }

                }
            });
        }
    </script>
</head>

<body>
    <form id="form1" runat="server">
        <div id="container">
            <div id="floatHead" class="content-tab-wrap" style="height: 42px;">
                <div class="content-tab" style="position: static; top: 52px;">
                    <div class="content-tab-ul-wrap">
                        <ul>
                            <li><a class="selected" href="javascript:void(0);">单个上传</a></li>
                            <li id="liBatch"><a href="javascript:void(0);">批量上传</a></li>
                            <asp:HiddenField runat="server" ID="hidUploadType" Value="0" />
                        </ul>
                    </div>
                </div>
            </div>
            <div class="tab-content">
                <div id="list" class="infoTable margin-bottom-bar">
                    <dl>
                        <dt><span class="red">*</span>上传文件</dt>
                        <dd>
                            <input runat="server" type="hidden" name="hidSingleFile" id="hidSingleFile" />
                            <div id="single" class="uploader" style="line-height: normal;"></div>
                            <div class="photo-list">
                                <ul></ul>
                            </div>
                            <p class="append">支持上传的文件类型<span runat="server" id="spanFileType1"></span></p>
                        </dd>
                    </dl>
                    <dl>
                        <dt>
                            <span class="red">*</span>文件标题
                        </dt>
                        <dd>
                            <asp:TextBox ID="txtTitle" runat="server" Width="200px"></asp:TextBox>
                        </dd>
                    </dl>
                    <dl>
                        <dt>文件描述</dt>
                        <dd>
                            <div class="act content" style="width: 99%;">
                                <asp:TextBox ID="txtContents" runat="server" Width="100%" Height="150px"
                                    TextMode="MultiLine"></asp:TextBox>
                                <asp:HiddenField ID="hidContent" runat="server" />
                            </div>
                        </dd>
                    </dl>
                    <dl>
                        <dt>是否公开</dt>
                        <dd>
                            <div class="rule-single-checkbox single-checkbox chkIsOpen">
                                <a href="javascript:void(0);"><i class="off">否</i><i class="on">是</i></a>
                                <asp:CheckBox ID="chkIsOpen" runat="server" Text="是" Style="display: none;" />
                            </div>
                        </dd>
                    </dl>
                </div>
            </div>
            <div id="dvBatch" class="tab-content" style="display: none">
                <dl>
                    <dt><span class="red">*</span>上传文件</dt>
                    <dd>
                        <input runat="server" type="hidden" name="hidMultipleFile" id="hidMultipleFile" />
                        <div id="multiple" class="uploader" style="line-height: normal;"></div>
                        <div class="photo-list">
                            <ul></ul>
                        </div>
                        <p class="append">支持上传的文件类型<span runat="server" id="spanFileType2"></span></p>
                    </dd>
                </dl>
                <dl style="padding-top: 10px">
                    <dt>是否公开</dt>
                    <dd>
                        <div class="rule-single-checkbox single-checkbox chkIsOpen">
                            <a href="javascript:void(0);"><i class="off">否</i><i class="on">是</i></a>
                            <asp:CheckBox ID="chkIsOpenMult" runat="server" Text="是" Style="display: none;" />
                        </div>
                    </dd>
                </dl>
                <asp:HiddenField runat="server" ID="hidSchool" />
                <asp:HiddenField runat="server" ID="hidDirectoryId" />
            </div>
            <div class="bottom-bar">
                <input id="btnAdd" type="button" value="保存" class="btnGreen" />
                <asp:Button ID="btnSave" runat="server" Text="保存" CssClass="btnGreen" OnClick="btnSave_Click"
                    Style="display: none" />&nbsp;
                    <input id="btnClose" type="button" value="关闭" class="btnGray" onclick="CloseBox()" />
            </div>
            <asp:HiddenField runat="server" ID="hidFileType" />
            <asp:HiddenField runat="server" ID="hidFileSize" />
        </div>
    </form>
</body>
<script>
    var fileSize;
    var fileType;
    var _currentIndex;
    var varObj = [];// 定义
    let uploadFiles = [];
    $(function () {
        fileSize = $("#hidFileSize").val();
        fileType = $("#hidFileType").val();

        //引用上传附件
        var singleFileList;
        if ($('#hidSingleFile').val() != "" && $('#hidSingleFile').val() != "[]") {
            singleFileList = JSON.parse($('#hidSingleFile').val());
            if (singleFileList.length) {
                uploadFiles = uploadFiles.concat(singleFileList);
            }
        }
        // 单个上传
        $('#single').initUploader({
            server: 'ajax/uploader.ashx',
            directoryId: $("#hidDirectoryId").val(),
            chunked: true,
            chunkSize: 5,// 1M
            multiple: false,
            fileNumLimit: 1,
            accept: fileType.replaceAll("*.", ""),
            fileSizeLimit: fileSize.replace("M", "") * 1024 * 1024,// M
            fileList: singleFileList,
            onSuccess: function (data) {
                //console.log('上传成功:', data);
                uploadFiles.push(data);
                $('#txtTitle').val(data.name);
            },
            onRemove: function (data) {
                if (data) {
                    //console.log('文件移除:', data);
                    uploadFiles.some(function (item, index) {
                        if (item.path === data.path) {
                            uploadFiles.splice(index, 1);
                            return true;
                        }
                    });
                    $('#txtTitle').val("");
                }
            }
        });
        // 批量上传
        var multipleFileList;
        if ($('#hidMultipleFile').val() != "" && $('#hidMultipleFile').val() != "[]") {
            multipleFileList = JSON.parse($('#hidMultipleFile').val());
        }
        $('#multiple').initUploader({
            server: 'ajax/uploader.ashx',
            directoryId: $("#hidDirectoryId").val(),
            chunked: true,
            chunkSize: 5,// 1M
            multiple: true,
            fileNumLimit: 10,
            accept: fileType.replaceAll("*.", ""),
            fileSizeLimit: fileSize.replace("M", "") * 1024 * 1024,// b
            fileList: multipleFileList,
            onSuccess: function (data) {
                uploadFiles.push(data);
            },
            onRemove: function (data) {
                if (data) {
                    uploadFiles.some(function (item, index) {
                        if (item.path === data.path) {
                            uploadFiles.splice(index, 1);
                            return true;
                        }
                    });
                }
            }
        });
        //是否置顶
        if ($("#chkIsOpen").attr("checked") == "checked")
            $(".rule-single-checkbox.single-checkbox.chkIsOpen a").addClass("selected");

        //是否置顶
        $(".rule-single-checkbox.single-checkbox.chkIsOpen").click(function () {
            if ($(this).find("a").toggleClass("selected").is(".selected")) {
                $("#chkIsOpen").attr("checked", true);
                $("#chkIsOpenMult").attr("checked", true);

            }
            else {
                $("#chkIsOpen").attr("checked", false);
                $("#chkIsOpenMult").attr("checked", false);
            }
        });

        //菜单栏
        $("#floatHead ul li a").click(function () {
            var index = $(this).parent().index();
            $(this).parent().siblings().find("a").removeClass("selected");
            $(this).addClass("selected");
            $(".tab-content").hide();
            $(".tab-content:eq(" + index + ")").show();
            $("#hidUploadType").val(index);
        });

        //是否添加
        if ("<%=IsAdd%>" == "0") {
            $("#dvBatch").hide();
            $("#liBatch").hide();
        }
    })
    //添加
    $("#btnAdd").click(function () {
        if (!uploadFiles.length) {
            layer.msg('请选择需要上传的文件');
            return false;
        }
        if ($("#hidUploadType").val() == "0") {
            $("#hidSingleFile").val(JSON.stringify(uploadFiles));
            if ($('#txtTitle').val() == "") {
                layer.msg('文件标题不能为空');
                return false;
            }
        } else {
            $("#hidMultipleFile").val(JSON.stringify(uploadFiles));
        }
        $("#btnSave").click();
    })
    //预览图片
    function FileImg(object, ImgName) {
        var _src = $(object).attr("src");
        var json = {
            "title": "", //相册标题
            "id": 123, //相册id
            "start": 0, //初始显示的图片序号，默认0
            "data": [   //相册包含的图片，数组格式
                {
                    "alt": ImgName,
                    "pid": 666, //图片id
                    "src": _src, //原图地址
                    "thumb": _src //缩略图地址
                }
            ]
        }
        layer.photos({
            photos: json, anim: 5, shade: 0.2
        });
    }
</script>
</html>
