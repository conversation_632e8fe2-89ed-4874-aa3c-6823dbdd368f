﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Data;
using Newtonsoft.Json;
using System.Web;
using System.Text;
using YunEdu.Common;
using System.Runtime.Serialization;
using System.Security.Cryptography;
using DMS.BLL;
namespace DMS.Web.dms.ajax
{
    /// <summary>
    /// upload 的摘要说明
    /// </summary>
    public class UploadFiles : IHttpHandler
    {
        BasePage ac = new BasePage();
        BLL.dms_files bllfiles = new BLL.dms_files();
        Model.dms_directory modelDirectory = new Model.dms_directory();
        BLL.dms_directory blldirectory = new BLL.dms_directory();
        Model.dms_files modelFiles = new Model.dms_files();
        BLL.dms_files bllFiles = new BLL.dms_files();
        Model.dms_auth modelAuth = new Model.dms_auth();
        BLL.dms_auth bllAuth = new BLL.dms_auth();
        DMS.BLL.dms_config bll_dms_config = new DMS.BLL.dms_config();
        public void ProcessRequest(HttpContext context)
        {
            context.Response.ContentType = "text/plain";
            context.Response.Charset = "utf-8";

            string Method = context.Request["Method"];
            switch (Method)
            {
                #region 批量上传文件
                case "upload_file"://上传文件
                    upload_file(context);
                    break;
                case "remove_file"://删除文件
                    remove_file(context);
                    break;
                #endregion

                #region 设置用户权限
                case "getgroup"://获取部门
                    getgroup(context);
                    break;
                case "getuser"://获取用户权限
                    getuser(context);
                    break;
                case "saveUserAuth"://保存用户权限
                    saveUserAuth(context);
                    break;
                #endregion
                default:
                    break;
            }
        }

        #region 删除文件
        private void remove_file(HttpContext context)
        {
            //上传临时目录
            string _folder = string.Format("/temp/{0}/", ac.UserName);
            //获取当前登录人临时目录
            string uploadPath = context.Server.MapPath(_folder);
            if (!string.IsNullOrEmpty(context.Request["file"]))
            {
                string filePath = uploadPath + context.Request["file"];//文件路径
                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                }
            }
        }
        #endregion

        #region 上传文件
        private void upload_file(HttpContext context)
        {
            try
            {
                // 从前端获取目录ID
                string strDirectoryId = context.Request.Form["directoryId"] ?? context.Request.QueryString["directoryId"];
                int nDirectoryId = 0;

                if (string.IsNullOrEmpty(strDirectoryId) || !int.TryParse(strDirectoryId, out nDirectoryId))
                {
                    context.Response.Write("{\"status\": 0, \"msg\": \"目录ID参数无效！\"}");
                    return;
                }

                // 获取目录信息
                Model.dms_directory directory = blldirectory.GetModel(nDirectoryId);
                if (directory == null)
                {
                    context.Response.Write("{\"status\": 0, \"msg\": \"目录不存在！\"}");
                    return;
                }

                // 获取FTP配置
                DMS.Model.dms_config ftpConfig = GetFtpConfigByColumnPath(directory.ColumnPath);

                List<object> uploadResults = new List<object>();

                // 上传文件
                for (int i = 0; i < context.Request.Files.Count; i++)
                {
                    HttpPostedFile file = context.Request.Files[i];

                    if (file != null && file.ContentLength > 0)
                    {
                        string fileName = Path.GetFileNameWithoutExtension(file.FileName);
                        string fileExt = Path.GetExtension(file.FileName).TrimStart('.');
                        string newFileName = GetRamCode() + "." + fileExt;
                        string fileMD5 = GetFileMd5(file);

                        // 根据MD5查找文件，如果有则复用
                        DMS.Model.dms_files existingFile = bllFiles.GetModelByMD5(fileMD5);
                        if (existingFile != null)
                        {
                            SaveFileToDatabase(file, directory, newFileName, existingFile.FilePath, existingFile.FileMD5);
                            continue;
                        }

                        if (IsFtpServer(ftpConfig))
                        {
                            // 上传到FTP服务器
                            var result = UploadToFtpServer(file, directory, newFileName, fileMD5, ftpConfig);
                            uploadResults.Add(result);
                        }
                        else
                        {
                            // 上传到本地服务器
                            var result = UploadToLocalServer(file, directory, newFileName, fileMD5);
                            uploadResults.Add(result);
                        }
                    }
                }

                // 返回上传结果
                context.Response.Write(Newtonsoft.Json.JsonConvert.SerializeObject(new
                {
                    status = 1,
                    msg = "上传完成",
                    results = uploadResults
                }));
            }
            catch (Exception ex)
            {
                context.Response.Write("{\"status\": 0, \"msg\": \"上传过程中发生错误：" + ex.Message + "\"}");
            }
        }
        #endregion

        #region 获取部门
        private void getgroup(HttpContext context)
        {
            YunEdu.BLL.JC_Department bll_dept = new YunEdu.BLL.JC_Department();
            // 获取地区下所有部门
            int id;
            DataTable dtGroup = null;
            if (Int32.TryParse(context.Request["id"], out id))
            {
                //获取子级部门
                dtGroup = bll_dept.GetList(" ParentId=" + id + " and ColumnId=" + ac.modelAreaUser.ColumnID).Tables[0];
            }
            else
            {
                //获取父级
                dtGroup = bll_dept.GetList(" ParentId=0 and ColumnId=" + ac.modelAreaUser.ColumnID).Tables[0];
            }
            // 判断是否有数据
            if (dtGroup != null && dtGroup.Rows.Count > 0)
            {
                context.Response.Write(JsonConvert.SerializeObject(dtGroup));
            }
        }
        #endregion

        #region 获取用户权限
        private void getuser(HttpContext context)
        {
            int GroupId = 0;
            int DirectoryId = 0;
            result _result = new result();
            YunEdu.Model.JC_Department modelDepartment = new YunEdu.Model.JC_Department();
            string _directoryid = context.Request.QueryString["directoryid"];
            string _groupid = context.Request.QueryString["groupid"];
            StringBuilder sbHtml = new StringBuilder();
            sbHtml.Append("<table id='atuhInfo' class='table stable'>");
            sbHtml.Append("<tr class='sheader'><th align='center' >人员</th><th align='center'><div class='d-flex '><input type=\"checkbox\" onclick=\"setAllChecked(this,1)\" />上传</div></th><th><div class='d-flex '><input type=\"checkbox\" onclick=\"setAllChecked(this,2)\" />编辑</div></th><th align='center'><div class='d-flex '><input type=\"checkbox\" onclick=\"setAllChecked(this,3)\" />删除</div></th></tr>");
            if (!string.IsNullOrEmpty(_directoryid)) int.TryParse(_directoryid, out DirectoryId);
            if (!string.IsNullOrEmpty(_groupid)) int.TryParse(_groupid, out GroupId);
            modelDirectory = blldirectory.GetModel(DirectoryId);
            modelDepartment = new YunEdu.BLL.JC_Department().GetModel(GroupId);
            //获取部门信息、文件夹信息
            if (modelDepartment != null && modelDirectory != null)
            {
                //获取该文件夹所有用户权限
                DataTable dtAuth = bllAuth.GetList("DirectoryId=" + modelDirectory.Id).Tables[0];
                DataTable dtUser = new YunEdu.BLL.JC_TeacherDept().GetUserInfos(ac.modelAreaUser.ColumnID, GroupId).Tables[0];
                if (dtUser != null && dtUser.Rows.Count > 0)
                {
                    _result.code = true;
                    foreach (DataRow item in dtUser.Rows)
                    {
                        string userName = item["TName"].ToString();
                        Guid userId = Guid.Parse(item["UserId"].ToString());
                        #region 获取用户权限
                        string addChecked = "";
                        string editChecked = "";
                        string delChecked = "";
                        DataRow[] drAuth = dtAuth.Select("UserId='" + userId + "'");
                        if (drAuth.Length > 0)
                        {
                            //权限（1上传、2编辑、3删除，多权限|分割）
                            string _auth = drAuth[0]["Auth"].ToString();
                            if (!string.IsNullOrEmpty(_auth))
                            {
                                if (_auth.IndexOf("1") > -1)
                                {
                                    addChecked = "checked='true'";
                                }
                                if (_auth.IndexOf("2") > -1)
                                {
                                    editChecked = "checked='true'";
                                }
                                if (_auth.IndexOf("3") > -1)
                                {
                                    delChecked = "checked='true'";
                                }
                            }
                        }
                        #endregion
                        sbHtml.Append("<tr class='srow'><td><span userId='" + userId + "'>" + userName + "</span></td>");
                        //上传
                        sbHtml.Append("<td><input id='chkAdd' type='checkbox' " + addChecked + " /></td>");
                        //编辑
                        sbHtml.Append("<td><input class='chkEdit' type='checkbox' " + editChecked + "/></td>");
                        //删除
                        sbHtml.Append("<td><input id='chkDel' type='checkbox' " + delChecked + "/></td></tr>");
                    }
                }
                else
                {
                    sbHtml.Append("<tr><td colspan='4'>暂无数据！</td></tr>");
                }

            }
            else
            {
                sbHtml.Append("<tr><td colspan='4'>暂无数据！</td></tr>");
            }
            sbHtml.Append("</table>");
            _result.msg = sbHtml.ToString();
            context.Response.Write(Newtonsoft.Json.JsonConvert.SerializeObject(_result));
        }
        #endregion

        #region 保存用户权限
        private void saveUserAuth(HttpContext context)
        {
            result _result = new result();
            _result.code = true;
            _result.msg = "设置成功！";
            //判断是否为校管
            if (ac.RoleLevel.Contains(((int)YunEdu.Common.CodeTable.AdminRoleLevel.SchoolAdmin).ToString()))
            {
                try
                {
                    int directoryId;
                    string _AuthInfo = context.Request.Form["AuthInfo"];
                    if (int.TryParse(context.Request.Form["directoryId"], out directoryId))
                    {
                        Model.dms_directory modelDirectory = new BLL.dms_directory().GetModel(directoryId);
                        if (modelDirectory != null)
                        {
                            List<authInfo> listauth = new List<authInfo>();
                            listauth = Newtonsoft.Json.JsonConvert.DeserializeObject<List<authInfo>>(_AuthInfo);
                            foreach (authInfo auth in listauth)
                            {
                                Guid userId = Guid.Parse(auth.UserId);
                                //无任何权限则删除该用户权限记录
                                if (string.IsNullOrEmpty(auth.Auth))
                                {
                                    bllAuth.Delete(userId, directoryId);
                                }
                                else
                                {
                                    //获取用户权限
                                    modelAuth = bllAuth.GetModel(userId, directoryId);
                                    if (modelAuth != null)
                                    {
                                        //如果进行了修改则进行添加操作
                                        if (!modelAuth.Auth.Equals(auth.Auth))
                                        {
                                            modelAuth.Auth = auth.Auth;
                                            modelAuth.CreateTime = DateTime.Now;
                                        }
                                    }
                                    else
                                    {
                                        modelAuth = new Model.dms_auth();
                                        modelAuth.ColumnId = ac.modelAreaUser.ColumnID;
                                        modelAuth.ColumnPath = ac.modelAreaUser.ColumnPath;
                                        modelAuth.DirectoryId = modelDirectory.Id;
                                        modelAuth.DirectoryPath = modelDirectory.Path;
                                        modelAuth.Auth = auth.Auth;
                                        modelAuth.UserId = userId;
                                        modelAuth.Creator = Guid.Parse(ac.UserId);
                                        modelAuth.CreateTime = DateTime.Now;
                                    }
                                    if (modelAuth != null)
                                    {
                                        bllAuth.Add(modelAuth);
                                    }
                                }
                            }
                        }
                        else
                        {
                            _result.code = false;
                            _result.msg = "参数错误！";
                        }
                    }
                }
                catch (Exception)
                {
                    _result.code = false;
                    _result.msg = "设置失败！";
                }
            }
            else
            {
                _result.code = false;
                _result.msg = "您不是管理员，暂无配置用户权限！";
            }
            context.Response.Write(Newtonsoft.Json.JsonConvert.SerializeObject(_result));
        }
        #endregion

        /// <summary>
        /// 用户权限信息
        /// </summary>
        public class authInfo
        {
            //用户id
            public string UserId { get; set; }
            //用户权限
            public string Auth { get; set; }
        }
        /// <summary>
        /// 用户权限信息
        /// </summary>
        public class result
        {
            //用户id
            public bool code { get; set; }
            //用户权限
            public string msg { get; set; }
        }

        #region FTP和上传相关方法

        /// <summary>
        /// 上传到FTP服务器
        /// </summary>
        private object UploadToFtpServer(HttpPostedFile file, Model.dms_directory directory, string newFileName, string fileMD5, DMS.Model.dms_config ftpConfig)
        {
            try
            {
                // 构建FTP路径
                string strFtpPath = GetFtpRootPath(ftpConfig) + newFileName;

                using (var ftpHelper = CreateFtpHelper(ftpConfig))
                {
                    if (ftpHelper != null)
                    {
                        // 上传文件
                        using (var fileStream = file.InputStream)
                        {
                            fileStream.Position = 0;
                            if (ftpHelper.UploadFileFromStream(fileStream, strFtpPath))
                            {
                                // 保存到数据库
                                SaveFileToDatabase(file, directory, newFileName, strFtpPath, fileMD5);

                                return new
                                {
                                    status = 1,
                                    fileName = file.FileName,
                                    newFileName = newFileName,
                                    path = strFtpPath,
                                    msg = "FTP上传成功"
                                };
                            }
                            else
                            {
                                return new
                                {
                                    status = 0,
                                    fileName = file.FileName,
                                    msg = "FTP上传失败"
                                };
                            }
                        }
                    }
                    else
                    {
                        return new
                        {
                            status = 0,
                            fileName = file.FileName,
                            msg = "无法连接到FTP服务器"
                        };
                    }
                }
            }
            catch (Exception ex)
            {
                return new
                {
                    status = 0,
                    fileName = file.FileName,
                    msg = "FTP上传错误：" + ex.Message
                };
            }
        }

        /// <summary>
        /// 上传到本地服务器
        /// </summary>
        private object UploadToLocalServer(HttpPostedFile file, Model.dms_directory directory, string newFileName,string fileMD5)
        {
            try
            {
                // 构建本地路径
                string strRelativePath = blldirectory.GetRootDirectory(directory.ColumnId);
                string strAbsolutePath = HttpContext.Current.Server.MapPath(strRelativePath);

                // 确保目录存在
                if (!Directory.Exists(strAbsolutePath))
                {
                    Directory.CreateDirectory(strAbsolutePath);
                }

                // 保存文件
                string strFilePath = Path.Combine(strAbsolutePath, newFileName);
                file.SaveAs(strFilePath);

                // 保存到数据库
                string strDbPath = strRelativePath + newFileName;
                SaveFileToDatabase(file, directory, newFileName, strDbPath, fileMD5);

                return new
                {
                    status = 1,
                    fileName = file.FileName,
                    newFileName = newFileName,
                    path = strDbPath,
                    msg = "本地上传成功"
                };
            }
            catch (Exception ex)
            {
                return new
                {
                    status = 0,
                    fileName = file.FileName,
                    msg = "本地上传错误：" + ex.Message
                };
            }
        }

        /// <summary>
        /// 保存文件信息到数据库
        /// </summary>
        private void SaveFileToDatabase(HttpPostedFile file, Model.dms_directory directory, string newFileName, string filePath, string md5)
        {
            try
            {
                Model.dms_files fileModel = new Model.dms_files();
                fileModel.FileName = Path.GetFileNameWithoutExtension(file.FileName);
                fileModel.FileFormat = Path.GetExtension(file.FileName).TrimStart('.');
                fileModel.FileSize = file.ContentLength;
                fileModel.FilePath = filePath;
                fileModel.DirectoryId = directory.Id;
                fileModel.DirectoryPath = directory.Path;
                fileModel.ColumnId = directory.ColumnId;
                fileModel.ColumnPath = directory.ColumnPath;
                fileModel.Creator = new Guid(ac.UserId);
                fileModel.CreateTime = DateTime.Now;
                fileModel.IsOpen = 0; // 默认不公开
                fileModel.FileType = bllFiles.getFileType(fileModel.FileFormat);
                fileModel.FileStatus = (int)CommonEnum.dms_FileStatus.Completed;
                fileModel.FileMD5 = md5;

                bllFiles.Add(fileModel);
            }
            catch (Exception)
            {
                // 记录日志，但不影响上传流程
                // 可以在这里添加日志记录
            }
        }

        /// <summary>
        /// 获取随机文件名
        /// </summary>
        private string GetRamCode()
        {
            return DateTime.Now.ToString("yyyyMMddHHmmss") + new Random().Next(1000, 9999).ToString();
        }

        #endregion

        #region FTP配置相关方法

        /// <summary>
        /// 根据ColumnPath向上查找FTP配置
        /// </summary>
        private DMS.Model.dms_config GetFtpConfigByColumnPath(string columnPath)
        {
            if (string.IsNullOrEmpty(columnPath))
                return null;
            try
            {
                return bll_dms_config.GetModel(columnPath);
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 判断是否为FTP服务器
        /// </summary>
        private bool IsFtpServer(DMS.Model.dms_config config)
        {
            if (config == null || string.IsNullOrEmpty(config.ServerUrl))
                return false;

            string strServerUrl = config.ServerUrl.ToLower();
            return strServerUrl.StartsWith("ftp://") || strServerUrl.Contains("ftp");
        }

        /// <summary>
        /// 创建FTP帮助类实例
        /// </summary>
        private FtpHelperFluentFTP CreateFtpHelper(DMS.Model.dms_config config)
        {
            if (config == null || !IsFtpServer(config))
                return null;

            string strServer = config.ServerUrl;
            if (strServer.ToLower().StartsWith("ftp://"))
            {
                strServer = strServer.Substring(6);
            }

            return new FtpHelperFluentFTP(strServer, config.FtpAccount, config.FtpPassword, config.FtpPort);
        }

        /// <summary>
        /// 获取FTP根路径
        /// </summary>
        /// <param name="config">FTP配置</param>
        /// <returns>FTP根路径</returns>
        private string GetFtpRootPath(Model.dms_config config)
        {
            if (config != null)
            {
                return $"/dms/doc/{config.ColumnId}/";
            }

            return $"/dms/doc/{ac.modelAreaUser.ColumnID}/";
        }

        /// <summary>
        /// 计算文件MD5值
        /// </summary>
        /// <param name="postedFile">上传的文件</param>
        /// <returns>MD5值</returns>
        private string GetFileMd5(HttpPostedFile postedFile)
        {
            // 不能释放stream，因为后续还要进行上传操作
            var stream = postedFile.InputStream;
            stream.Position = 0; // 重置流位置
            using (var md5 = MD5.Create())
            {
                byte[] hash = md5.ComputeHash(stream);
                return BitConverter.ToString(hash).Replace("-", "").ToLower();
            }
        }

        #endregion

        public bool IsReusable
        {
            get
            {
                return false;
            }
        }
    }
}