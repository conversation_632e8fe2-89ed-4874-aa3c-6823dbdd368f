﻿using System;
namespace DMS.Model
{
	/// <summary>
	/// dms_files:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class dms_files
	{
		public dms_files()
		{}
		#region Model
		private Guid _id;
		private string _filename;
		private int _filetype;
		private string _filepath;
		private Guid _creator;
		private DateTime? _createtime;
		private int _directoryid;
		private string _directorypath;
		private string _memo;
		private int _columnid;
		private string _columnpath;
		private int _isopen;
		private string _fileformat;
		private int _filesize;
		private int _filestatus;
		private string _filemd5;
		/// <summary>
		/// 编号
		/// </summary>
		public Guid Id
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 文件名
		/// </summary>
		public string FileName
		{
			set{ _filename=value;}
			get{return _filename;}
		}
		/// <summary>
		/// 文件类型
		/// </summary>
		public int FileType
		{
			set{ _filetype=value;}
			get{return _filetype;}
		}
		/// <summary>
		/// 文件路径(相对路径)
		/// </summary>
		public string FilePath
		{
			set{ _filepath=value;}
			get{return _filepath;}
		}
		/// <summary>
		/// 创建人
		/// </summary>
		public Guid Creator
		{
			set{ _creator=value;}
			get{return _creator;}
		}
		/// <summary>
		/// 创建时间
		/// </summary>
		public DateTime? CreateTime
		{
			set{ _createtime=value;}
			get{return _createtime;}
		}
		/// <summary>
		/// 所属文件夹
		/// </summary>
		public int DirectoryId
		{
			set{ _directoryid=value;}
			get{return _directoryid;}
		}
		/// <summary>
		/// 文件夹路径
		/// </summary>
		public string DirectoryPath
		{
			set{ _directorypath=value;}
			get{return _directorypath;}
		}
		/// <summary>
		/// 描述
		/// </summary>
		public string Memo
		{
			set{ _memo=value;}
			get{return _memo;}
		}
		/// <summary>
		/// 学校Id
		/// </summary>
		public int ColumnId
		{
			set{ _columnid=value;}
			get{return _columnid;}
		}
		/// <summary>
		/// 学校路径
		/// </summary>
		public string ColumnPath
		{
			set{ _columnpath=value;}
			get{return _columnpath;}
		}
		/// <summary>
		/// 是否公开（0不公开1公开）
		/// </summary>
		public int IsOpen
		{
			set{ _isopen=value;}
			get{return _isopen;}
		}
		/// <summary>
		/// 文件类型
		/// </summary>
		public string FileFormat
		{
			set{ _fileformat=value;}
			get{return _fileformat;}
		}
		/// <summary>
		/// 文件大小（KB）
		/// </summary>
		public int FileSize
		{
			set{ _filesize=value;}
			get{return _filesize;}
		}
		/// <summary>
		/// 文件状态（1：上传中，2：上传完成）
		/// </summary>
		public int FileStatus
		{
			set{ _filestatus=value;}
			get{return _filestatus;}
		}
		/// <summary>
		/// 文件MD5值
		/// </summary>
		public string FileMD5
		{
			set{ _filemd5=value;}
			get{return _filemd5;}
		}
		#endregion Model

	}
}

