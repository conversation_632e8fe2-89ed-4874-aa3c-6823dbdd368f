﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;//Please add references
namespace DMS.BLL
{
    /// <summary>
    /// 数据访问类:dms_directory
    /// </summary>
    public partial class dms_directory
    {
        public dms_directory()
        {
        }
        #region  BasicMethod

        /// <summary>
        /// 得到最大ID
        /// </summary>
        public int GetMaxId()
        {
            return DbHelperSQL.GetMaxID("Id", "dms_directory");
        }

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(int Id)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from dms_directory");
            strSql.Append(" where Id=@Id");
            SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.Int,4)
			};
            parameters[0].Value = Id;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }

        /// <summary>
        ///  增加一条数据
        /// </summary>
        public int Add(Model.dms_directory model)
        {
            int rowsAffected;
            SqlParameter[] parameters = {
                    new SqlParameter("@Id", SqlDbType.Int,4),
					new SqlParameter("@Name", SqlDbType.NVarChar,50),
					new SqlParameter("@ParentId", SqlDbType.Int,4),
					new SqlParameter("@ColumnId", SqlDbType.Int,4),
					new SqlParameter("@ColumnPath", SqlDbType.NVarChar,100),
					new SqlParameter("@Creator", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@CreateTime", SqlDbType.DateTime),
                    new SqlParameter("@OrderId", SqlDbType.Int,4),
                    new SqlParameter("@DeptId", SqlDbType.Int,4)
                    };
            parameters[0].Direction = ParameterDirection.Output;
            parameters[1].Value = model.Name;
            parameters[2].Value = model.ParentId;
            parameters[3].Value = model.ColumnId;
            parameters[4].Value = model.ColumnPath;
            parameters[5].Value = model.Creator;
            parameters[6].Value = model.CreateTime;
            parameters[7].Value = model.OrderId;
            parameters[8].Value = model.DeptId;

            DbHelperSQL.RunProcedure("UP_dms_directory_ADD", parameters, out rowsAffected);
            if (parameters[0].Value != null)
            {
                return int.Parse(parameters[0].Value.ToString());
            }
            else
            {
                return 0;
            }
        }

        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(DMS.Model.dms_directory model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update dms_directory set ");
            strSql.Append("Name=@Name,");
            strSql.Append("ParentId=@ParentId,");
            strSql.Append("ColumnId=@ColumnId,");
            strSql.Append("ColumnPath=@ColumnPath,");
            strSql.Append("Creator=@Creator,");
            strSql.Append("CreateTime=@CreateTime,");
            strSql.Append("OrderId=@OrderId,");
            strSql.Append("Path=@Path,");
            strSql.Append("Depth=@Depth,");
            strSql.Append("DeptId=@DeptId");
            strSql.Append(" where Id=@Id");
            SqlParameter[] parameters = {
					new SqlParameter("@Name", SqlDbType.NVarChar,50),
					new SqlParameter("@ParentId", SqlDbType.Int,4),
					new SqlParameter("@ColumnId", SqlDbType.Int,4),
					new SqlParameter("@ColumnPath", SqlDbType.NVarChar,100),
					new SqlParameter("@Creator", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@CreateTime", SqlDbType.DateTime),
					new SqlParameter("@OrderId", SqlDbType.Int,4),
					new SqlParameter("@Path", SqlDbType.NVarChar,100),
					new SqlParameter("@Depth", SqlDbType.Int,4),
					new SqlParameter("@DeptId", SqlDbType.Int,4),
					new SqlParameter("@Id", SqlDbType.Int,4)};
            parameters[0].Value = model.Name;
            parameters[1].Value = model.ParentId;
            parameters[2].Value = model.ColumnId;
            parameters[3].Value = model.ColumnPath;
            parameters[4].Value = model.Creator;
            parameters[5].Value = model.CreateTime;
            parameters[6].Value = model.OrderId;
            parameters[7].Value = model.Path;
            parameters[8].Value = model.Depth;
            parameters[9].Value = model.DeptId;
            parameters[10].Value = model.Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(int Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from dms_directory ");
            strSql.Append(" where Id=@Id");
            SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.Int,4)
			};
            parameters[0].Value = Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string Idlist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from dms_directory ");
            strSql.Append(" where Id in (" + Idlist + ")  ");
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public DMS.Model.dms_directory GetModel(int Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 Id,Name,ParentId,ColumnId,ColumnPath,Creator,CreateTime,OrderId,Path,Depth,DeptId from dms_directory ");
            strSql.Append(" where Id=@Id");
            SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.Int,4)
			};
            parameters[0].Value = Id;

            DMS.Model.dms_directory model = new DMS.Model.dms_directory();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public DMS.Model.dms_directory DataRowToModel(DataRow row)
        {
            DMS.Model.dms_directory model = new DMS.Model.dms_directory();
            if (row != null)
            {
                if (row["Id"] != null && row["Id"].ToString() != "")
                {
                    model.Id = int.Parse(row["Id"].ToString());
                }
                if (row["Name"] != null)
                {
                    model.Name = row["Name"].ToString();
                }
                if (row["ParentId"] != null && row["ParentId"].ToString() != "")
                {
                    model.ParentId = int.Parse(row["ParentId"].ToString());
                }
                if (row["ColumnId"] != null && row["ColumnId"].ToString() != "")
                {
                    model.ColumnId = int.Parse(row["ColumnId"].ToString());
                }
                if (row["ColumnPath"] != null)
                {
                    model.ColumnPath = row["ColumnPath"].ToString();
                }
                if (row["Creator"] != null && row["Creator"].ToString() != "")
                {
                    model.Creator = new Guid(row["Creator"].ToString());
                }
                if (row["CreateTime"] != null && row["CreateTime"].ToString() != "")
                {
                    model.CreateTime = DateTime.Parse(row["CreateTime"].ToString());
                }
                if (row["OrderId"] != null && row["OrderId"].ToString() != "")
                {
                    model.OrderId = int.Parse(row["OrderId"].ToString());
                }
                if (row["Path"] != null)
                {
                    model.Path = row["Path"].ToString();
                }
                if (row["Depth"] != null && row["Depth"].ToString() != "")
                {
                    model.Depth = int.Parse(row["Depth"].ToString());
                }
                if (row["DeptId"] != null && row["DeptId"].ToString() != "")
                {
                    model.DeptId = int.Parse(row["DeptId"].ToString());
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select Id,Name,ParentId,ColumnId,ColumnPath,Creator,CreateTime,OrderId,Path,Depth,DeptId ");
            strSql.Append(" FROM dms_directory ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" Id,Name,ParentId,ColumnId,ColumnPath,Creator,CreateTime,OrderId,Path,Depth,DeptId ");
            strSql.Append(" FROM dms_directory ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM dms_directory ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.Id desc");
            }
            strSql.Append(")AS Row, T.*  from dms_directory T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /*
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetList(int PageSize,int PageIndex,string strWhere)
        {
            SqlParameter[] parameters = {
                    new SqlParameter("@tblName", SqlDbType.VarChar, 255),
                    new SqlParameter("@fldName", SqlDbType.VarChar, 255),
                    new SqlParameter("@PageSize", SqlDbType.Int),
                    new SqlParameter("@PageIndex", SqlDbType.Int),
                    new SqlParameter("@IsReCount", SqlDbType.Bit),
                    new SqlParameter("@OrderType", SqlDbType.Bit),
                    new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
                    };
            parameters[0].Value = "dms_directory";
            parameters[1].Value = "Id";
            parameters[2].Value = PageSize;
            parameters[3].Value = PageIndex;
            parameters[4].Value = 0;
            parameters[5].Value = 0;
            parameters[6].Value = strWhere;	
            return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
        }*/

        #endregion  BasicMethod
        #region  ExtensionMethod

        /// <summary>
        /// 获取学校文档目录根节点
        /// </summary>
        /// <param name="columnId">地区id</param>
        /// <returns></returns>
        public string GetRootDirectory(int columnId)
        {
            return string.Format("/dms/doc/{0}/", columnId);
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        /// <param name="columnId">地区id</param>
        /// <returns></returns>
        public Model.dms_directory GetRoot(int columnId)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select * FROM dms_directory where ParentId=@ParentId and ColumnId=@ColumnId");
            SqlParameter[] parameters = {
					new SqlParameter("@ParentId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnId", SqlDbType.Int,4)
			};
            parameters[0].Value = 0;
            parameters[1].Value = columnId;
            DMS.Model.dms_directory model = new DMS.Model.dms_directory();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }

        ///// <summary>
        ///// 获取记录总数
        ///// </summary>
        ///// <param name="columnId">地区id</param>
        ///// <param name="parentId">父级id</param>
        ///// <returns></returns>
        //public Model.dms_directory GetModel(int columnId,int parentId)
        //{
        //    StringBuilder strSql = new StringBuilder();
        //    strSql.Append("select * FROM dms_directory where ParentId=@ParentId and ColumnId=@ColumnId");
        //    SqlParameter[] parameters = {
        //            new SqlParameter("@ParentId", SqlDbType.Int,4),
        //            new SqlParameter("@ColumnId", SqlDbType.Int,4)
        //    };
        //    parameters[0].Value = parentId;
        //    parameters[1].Value = columnId;
        //    DMS.Model.dms_directory model = new DMS.Model.dms_directory();
        //    DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
        //    if (ds.Tables[0].Rows.Count > 0)
        //    {
        //        return DataRowToModel(ds.Tables[0].Rows[0]);
        //    }
        //    else
        //    {
        //        return null;
        //    }
        //}

        /// <summary>
        /// 获得数据列表
        /// </summary>
        /// <param name="columnId">地区id</param>
        /// <returns></returns>
        public DataSet GetList(int columnId, int parentId)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select Id,Name,ParentId,ColumnId,ColumnPath,Creator,CreateTime,OrderId,Path,Depth,DeptId ");
            strSql.Append(" FROM dms_directory where ParentId=@ParentId and ColumnId=@ColumnId order by CreateTime");
            SqlParameter[] parameters = {
					new SqlParameter("@ParentId", SqlDbType.Int,4),
                    new SqlParameter("@ColumnId", SqlDbType.Int,4)
			};
            parameters[0].Value = parentId;
            parameters[1].Value = columnId;
            return DbHelperSQL.Query(strSql.ToString(), parameters);
        }

        /// <summary>
        /// 检查目录名是否存在
        /// </summary>
        /// <param name="parentId">父目录ID</param>
        /// <param name="directoryName">目录名称</param>
        /// <returns>是否存在</returns>
        public bool ExistsDirectoryName(int parentId, string directoryName)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from dms_directory");
            strSql.Append(" where ParentId=@ParentId AND Name=@Name");
            SqlParameter[] parameters = {
                    new SqlParameter("@ParentId", SqlDbType.Int,4),
                    new SqlParameter("@Name", SqlDbType.NVarChar,50)
            };
            parameters[0].Value = parentId;
            parameters[1].Value = directoryName;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }

        /// <summary>
        /// 根据父目录ID和名称获取目录列表
        /// </summary>
        /// <param name="parentId">父目录ID</param>
        /// <param name="directoryName">目录名称</param>
        /// <returns>目录列表</returns>
        public DataSet GetListByParentIdAndName(int parentId, string directoryName)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select Id,Name,ParentId,ColumnId,ColumnPath,Creator,CreateTime,OrderId,Path,Depth,DeptId ");
            strSql.Append(" FROM dms_directory where ParentId=@ParentId AND Name=@Name");
            SqlParameter[] parameters = {
                    new SqlParameter("@ParentId", SqlDbType.Int,4),
                    new SqlParameter("@Name", SqlDbType.NVarChar,50)
            };
            parameters[0].Value = parentId;
            parameters[1].Value = directoryName;
            return DbHelperSQL.Query(strSql.ToString(), parameters);
        }


        #endregion  ExtensionMethod
    }
}

