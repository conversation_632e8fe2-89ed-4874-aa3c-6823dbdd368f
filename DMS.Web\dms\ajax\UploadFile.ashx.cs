﻿using System;
using System.IO;
using System.Security.Cryptography;
using System.Web;
using System.Web.SessionState;
using static DMS.Web.dms.ajax.UploadFiles;

namespace DMS.Web.dms.ajax
{
    /// <summary>
    /// UploadFile 的摘要说明
    /// </summary>
    public class UploadFile : IHttp<PERSON><PERSON><PERSON>, IRequiresSessionState
    {
        BasePage _ac = new BasePage();
        BLL.dms_directory bll_dms_directory = new BLL.dms_directory();
        BLL.dms_config bll_dms_config = new BLL.dms_config();
        BLL.dms_files bll_dms_files = new BLL.dms_files();

        public void ProcessRequest(HttpContext context)
        {
            var result = new UploadFileResResult();
            if (!context.User.Identity.IsAuthenticated)
            {
                result.status = 0;
                result.msg = "登录超时，请重新登录！";
                context.Response.Write(Newtonsoft.Json.JsonConvert.SerializeObject(result));
                return;
            }

            string _action = context.Request.QueryString["action"];
            switch (_action)
            {
                case "UpLoadFile":
                    result = UpLoadFile(context);
                    break;
                default:
                    break;
            }
            context.Response.Write(Newtonsoft.Json.JsonConvert.SerializeObject(result));
        }

        /// <summary>
        /// 上传文件
        /// </summary>
        /// <param name="context"></param>
        private UploadFileResResult UpLoadFile(HttpContext context)
        {
            var result = new UploadFileResResult();
            HttpPostedFile postedFile = context.Request.Files["Filedata"];
            if (postedFile == null || postedFile.ContentLength == 0)
            {
                result.status = 0;
                result.msg = "请选择要上传的文件！";
                return result;
            }

            // 从QueryString获取directoryId
            string strDirectoryId = HttpContext.Current.Request.QueryString["directoryId"];
            int nDirectoryId = 0;

            Model.dms_directory directory;
            if (string.IsNullOrEmpty(strDirectoryId) || !int.TryParse(strDirectoryId, out nDirectoryId))
            {
                // 如果没有directoryId，使用默认根目录
                directory = bll_dms_directory.GetRoot(_ac.modelAreaUser.ColumnID);
            }
            else
            {
                directory = bll_dms_directory.GetModel(nDirectoryId);
            }
            if (directory == null)
            {
                result.status = 0;
                result.msg = $"指定的目录不存在！";
                return result;
            }
            // 处理文件
            UploadFileInfo uploadFileInfo = new UploadFileInfo();
            uploadFileInfo.FileName = postedFile.FileName.Substring(postedFile.FileName.LastIndexOf(@"\") + 1);
            uploadFileInfo.FileExt = GetFileExt(postedFile.FileName);
            uploadFileInfo.FileSize = postedFile.ContentLength;
            uploadFileInfo.SaveName = GetRamCode() + "." + uploadFileInfo.FileExt;
            uploadFileInfo.FileMD5 = GetFileMd5(postedFile);

            return SaveFile(postedFile, uploadFileInfo, directory);
        }

        /// <summary>
        /// 保存文件
        /// </summary>
        /// <param name="uploadFile">上传的图片</param>
        /// <returns></returns>
        public UploadFileResResult SaveFile(HttpPostedFile postedFile, UploadFileInfo uploadFile, Model.dms_directory directory)
        {
            var result = new UploadFileResResult();
            try
            {
                // 检查MD5是否已存在
                DMS.Model.dms_files existingFile = bll_dms_files.GetModelByMD5(uploadFile.FileMD5);
                if (existingFile != null)
                {
                    result.status = 1;
                    result.msg = "文件已存在!";
                    result.name = existingFile.FileName;
                    result.path = existingFile.FilePath;
                    result.thumb = existingFile.FilePath;
                    result.size = existingFile.FileSize;
                    result.ext = existingFile.FileFormat;
                    result.md5 = existingFile.FileMD5;
                    return result;
                }

                // 新文件上传
                return UploadNewFile(postedFile, uploadFile, directory);
            }
            catch (Exception ex)
            {
                result.status = 0;
                result.msg = $"上传过程中发生意外错误:{ex.Message}";
                return result;
            }
        }

        /// <summary>
        /// 返回文件扩展名，不含“.”
        /// </summary>
        /// <param name="_filepath">文件全名称</param>
        /// <returns>string</returns>
        public string GetFileExt(string _filepath)
        {
            if (string.IsNullOrEmpty(_filepath))
            {
                return "";
            }
            if (_filepath.LastIndexOf(".") > 0)
            {
                return _filepath.Substring(_filepath.LastIndexOf(".") + 1); //文件扩展名，不含“.”
            }
            return "";
        }

        /// <summary>
        /// 生成日期随机码
        /// </summary>
        /// <returns></returns>
        public string GetRamCode()
        {
            return DateTime.Now.ToString("yyyyMMddHHmmssffff");
        }

        #region 文件上传

        /// <summary>
        /// 根据ColumnPath向上查找FTP配置
        /// </summary>
        /// <param name="columnPath">地区路径</param>
        /// <returns>FTP配置信息</returns>
        private Model.dms_config GetFtpConfigByColumnPath(string columnPath)
        {
            if (string.IsNullOrEmpty(columnPath))
                return null;
            try
            {
                return bll_dms_config.GetModel(columnPath);
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 判断是否为FTP服务器
        /// </summary>
        /// <param name="config">配置信息</param>
        /// <returns>是否为FTP服务器</returns>
        private bool IsFtpServer(Model.dms_config config)
        {
            if (config == null || string.IsNullOrEmpty(config.ServerUrl))
                return false;

            string strServerUrl = config.ServerUrl.ToLower();
            return strServerUrl.StartsWith("ftp://") || strServerUrl.Contains("ftp");
        }

        /// <summary>
        /// 创建FTP帮助类实例
        /// </summary>
        /// <param name="config">配置信息</param>
        /// <returns>FTP帮助类实例</returns>
        private FtpHelperFluentFTP CreateFtpHelper(Model.dms_config config)
        {
            if (config == null || !IsFtpServer(config))
                return null;

            string strServer = config.ServerUrl;
            if (strServer.ToLower().StartsWith("ftp://"))
            {
                strServer = strServer.Substring(6);
            }

            return new FtpHelperFluentFTP(strServer, config.FtpAccount, config.FtpPassword, config.FtpPort);
        }

        /// <summary>
        /// 获取FTP根路径
        /// </summary>
        /// <param name="config">FTP配置</param>
        /// <returns>FTP根路径</returns>
        private string GetFtpRootPath(Model.dms_config config)
        {
            if (config != null)
            {
                return $"/dms/doc/{config.ColumnId}/";
            }

            return $"/dms/doc/{_ac.modelAreaUser.ColumnID}/";
        }

        /// <summary>
        /// 计算文件MD5值
        /// </summary>
        /// <param name="postedFile">上传的文件</param>
        /// <returns>MD5值</returns>
        private string GetFileMd5(HttpPostedFile postedFile)
        {
            // 不能释放stream，因为后续还要进行上传操作
            var stream = postedFile.InputStream;
            stream.Position = 0; // 重置流位置
            using (var md5 = MD5.Create())
            {
                byte[] hash = md5.ComputeHash(stream);
                return BitConverter.ToString(hash).Replace("-", "").ToLower();
            }
        }

        /// <summary>
        /// 上传新文件
        /// </summary>
        private UploadFileResResult UploadNewFile(HttpPostedFile postedFile, UploadFileInfo uploadFileInfo, Model.dms_directory directory)
        {
            // 获取FTP配置
            DMS.Model.dms_config ftpConfig = GetFtpConfigByColumnPath(directory.ColumnPath);

            if (IsFtpServer(ftpConfig))
            {
                // FTP服务器上传
                return UploadNewFileToFtp(postedFile, uploadFileInfo, directory, ftpConfig);
            }
            else
            {
                // 本地服务器上传
                return UploadNewFileToLocal(postedFile, uploadFileInfo, directory);
            }
        }

        /// <summary>
        /// 上传新文件到FTP服务器
        /// </summary>
        private UploadFileResResult UploadNewFileToFtp(HttpPostedFile postedFile, UploadFileInfo uploadFile, Model.dms_directory directory, Model.dms_config ftpConfig)
        {
            var result = new UploadFileResResult();
            try
            {
                // 获取FTP根路径
                string strFtpRootPath = GetFtpRootPath(ftpConfig);
                string strFtpFilePath = $"{strFtpRootPath.TrimEnd('/')}/{uploadFile.SaveName}";
                uploadFile.FilePath = strFtpFilePath;

                using (var ftpHelper = CreateFtpHelper(ftpConfig))
                {
                    if (ftpHelper == null)
                    {
                        result.status = 0;
                        result.msg = "无法连接到FTP服务器！";
                        return result;
                    }

                    // 上传文件
                    using (var fileStream = postedFile.InputStream)
                    {
                        // 重置位置
                        fileStream.Position = 0;
                        if (ftpHelper.UploadFileFromStreamWithResume(fileStream, strFtpFilePath))
                        {
                            result.status = 1;
                            result.msg = "上传文件成功!";
                            result.name = uploadFile.FileName;
                            result.path = uploadFile.FilePath;
                            result.thumb = uploadFile.FilePath;
                            result.size = uploadFile.FileSize;
                            result.ext = uploadFile.FileExt;
                            result.md5 = uploadFile.FileMD5;
                            return result;
                        }
                        else
                        {
                            result.status = 0;
                            result.msg = "FTP上传失败！";
                            return result;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                result.status = 0;
                result.msg = $"FTP上传错误：{ex.Message}";
                return result;
            }
        }

        /// <summary>
        /// 上传新文件到本地服务器
        /// </summary>
        private UploadFileResResult UploadNewFileToLocal(HttpPostedFile postedFile, UploadFileInfo uploadFile, Model.dms_directory directory)
        {
            var result = new UploadFileResResult();
            try
            {
                // 构建本地路径
                string strLocalPath = bll_dms_directory.GetRootDirectory(_ac.modelAreaUser.ColumnID) + uploadFile.SaveName;

                // 确保目录存在
                string strAbsolutePath = HttpContext.Current.Server.MapPath(strLocalPath);
                string strDirectoryPath = Path.GetDirectoryName(strAbsolutePath);
                if (!Directory.Exists(strDirectoryPath))
                {
                    Directory.CreateDirectory(strDirectoryPath);
                }

                // 保存文件
                postedFile.SaveAs(strAbsolutePath);

                result.status = 1;
                result.msg = "上传文件成功!";
                result.name = uploadFile.FileName;
                result.path = uploadFile.FilePath;
                result.thumb = uploadFile.FilePath;
                result.size = uploadFile.FileSize;
                result.ext = uploadFile.FileExt;
                result.md5 = uploadFile.FileMD5;
                return result;
            }
            catch (Exception ex)
            {
                result.status = 0;
                result.msg = $"本地上传错误：{ex.Message}";
                return result;
            }
        }

        #endregion

        public bool IsReusable
        {
            get
            {
                return false;
            }
        }
    }
}