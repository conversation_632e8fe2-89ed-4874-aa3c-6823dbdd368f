﻿using System;
namespace DMS.Model
{
	/// <summary>
	/// 文档管理系统配置表
	/// </summary>
	[Serializable]
	public partial class dms_config
	{
		public dms_config()
		{}
		#region Model
		private Guid _id;
		private int _columnid;
		private string _columnpath;
		private string _serverurl;
		private int _ftpport;
		private string _ftpaccount;
		private string _ftppassword;
		private Guid _lasteditby;
		private DateTime _lastedittime;
		/// <summary>
		/// 主键
		/// </summary>
		public Guid Id
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 地区/学校Id
		/// </summary>
		public int ColumnId
		{
			set{ _columnid=value;}
			get{return _columnid;}
		}
		/// <summary>
		/// 地区/学校Id路径
		/// </summary>
		public string ColumnPath
		{
			set{ _columnpath=value;}
			get{return _columnpath;}
		}
		/// <summary>
		/// 服务器地址 默认是/
		/// </summary>
		public string ServerUrl
		{
			set{ _serverurl=value;}
			get{return _serverurl;}
		}
		/// <summary>
		/// ftp端口号 默认21
		/// </summary>
		public int FtpPort
		{
			set{ _ftpport=value;}
			get{return _ftpport;}
		}
		/// <summary>
		/// ftp账号
		/// </summary>
		public string FtpAccount
		{
			set{ _ftpaccount=value;}
			get{return _ftpaccount;}
		}
		/// <summary>
		/// ftp账号密码
		/// </summary>
		public string FtpPassword
		{
			set{ _ftppassword=value;}
			get{return _ftppassword;}
		}
		/// <summary>
		/// 最后修改人
		/// </summary>
		public Guid LastEditBy
		{
			set{ _lasteditby=value;}
			get{return _lasteditby;}
		}
		/// <summary>
		/// 最后修改时间
		/// </summary>
		public DateTime LastEditTime
		{
			set{ _lastedittime=value;}
			get{return _lastedittime;}
		}
		#endregion Model

	}
}

