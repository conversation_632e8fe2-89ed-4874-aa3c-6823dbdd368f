﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;//Please add references
namespace DMS.BLL
{
    /// <summary>
    /// 数据访问类:dms_auth
    /// </summary>
    public partial class dms_auth
    {
        public dms_auth()
        { }
        #region  BasicMethod

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(Guid Id)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from dms_auth");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)			};
            parameters[0].Value = Id;

            return DbHelperSQL.Exists(strSql.ToString(), parameters);
        }


        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(DMS.Model.dms_auth model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from dms_auth where UserId=@UserId and DirectoryId=@DirectoryId;");
            strSql.Append("insert into dms_auth(");
            strSql.Append("Id,ColumnId,ColumnPath,DirectoryId,DirectoryPath,Auth,UserId,Creator,CreateTime)");
            strSql.Append(" values (");
            strSql.Append("@Id,@ColumnId,@ColumnPath,@DirectoryId,@DirectoryPath,@Auth,@UserId,@Creator,@CreateTime)");
            SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@ColumnId", SqlDbType.Int,4),
					new SqlParameter("@ColumnPath", SqlDbType.NVarChar,100),
					new SqlParameter("@DirectoryId", SqlDbType.Int,4),
					new SqlParameter("@DirectoryPath", SqlDbType.NVarChar,100),
					new SqlParameter("@Auth", SqlDbType.NVarChar,10),
					new SqlParameter("@UserId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@Creator", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@CreateTime", SqlDbType.DateTime)};
            parameters[0].Value = Guid.NewGuid();
            parameters[1].Value = model.ColumnId;
            parameters[2].Value = model.ColumnPath;
            parameters[3].Value = model.DirectoryId;
            parameters[4].Value = model.DirectoryPath;
            parameters[5].Value = model.Auth;
            parameters[6].Value = model.UserId;
            parameters[7].Value = model.Creator;
            parameters[8].Value = model.CreateTime;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(DMS.Model.dms_auth model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update dms_auth set ");
            strSql.Append("ColumnId=@ColumnId,");
            strSql.Append("ColumnPath=@ColumnPath,");
            strSql.Append("DirectoryId=@DirectoryId,");
            strSql.Append("DirectoryPath=@DirectoryPath,");
            strSql.Append("Auth=@Auth,");
            strSql.Append("UserId=@UserId,");
            strSql.Append("Creator=@Creator,");
            strSql.Append("CreateTime=@CreateTime");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
					new SqlParameter("@ColumnId", SqlDbType.Int,4),
					new SqlParameter("@ColumnPath", SqlDbType.NVarChar,100),
					new SqlParameter("@DirectoryId", SqlDbType.Int,4),
					new SqlParameter("@DirectoryPath", SqlDbType.NVarChar,100),
					new SqlParameter("@Auth", SqlDbType.NVarChar,10),
					new SqlParameter("@UserId", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@Creator", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@CreateTime", SqlDbType.DateTime),
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)};
            parameters[0].Value = model.ColumnId;
            parameters[1].Value = model.ColumnPath;
            parameters[2].Value = model.DirectoryId;
            parameters[3].Value = model.DirectoryPath;
            parameters[4].Value = model.Auth;
            parameters[5].Value = model.UserId;
            parameters[6].Value = model.Creator;
            parameters[7].Value = model.CreateTime;
            parameters[8].Value = model.Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(Guid Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from dms_auth ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)			};
            parameters[0].Value = Id;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(Guid userId, int directoryId)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from dms_auth ");
            strSql.Append(" where UserId=@UserId and DirectoryId=@DirectoryId");
            SqlParameter[] parameters = {
					new SqlParameter("@UserId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@DirectoryId", SqlDbType.Int,4)};
            parameters[0].Value = userId;
            parameters[1].Value = directoryId;

            int rows = DbHelperSQL.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string Idlist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from dms_auth ");
            strSql.Append(" where Id in (" + Idlist + ")  ");
            int rows = DbHelperSQL.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public DMS.Model.dms_auth GetModel(Guid Id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 Id,ColumnId,ColumnPath,DirectoryId,DirectoryPath,Auth,UserId,Creator,CreateTime from dms_auth ");
            strSql.Append(" where Id=@Id ");
            SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.UniqueIdentifier,16)			};
            parameters[0].Value = Id;

            DMS.Model.dms_auth model = new DMS.Model.dms_auth();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }

        /// <summary>
        /// 根据用户Id和目录Id得到一个对象实体
        /// </summary>
        public DMS.Model.dms_auth GetModel(Guid UserId, int DirectoryId)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 Id,ColumnId,ColumnPath,DirectoryId,DirectoryPath,Auth,UserId,Creator,CreateTime from dms_auth ");
            strSql.Append(" where UserId=@UserId and DirectoryId=@DirectoryId ");
            SqlParameter[] parameters = {
					new SqlParameter("@UserId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@DirectoryId", SqlDbType.Int,4)};
            parameters[0].Value = UserId;
            parameters[1].Value = DirectoryId;

            DMS.Model.dms_auth model = new DMS.Model.dms_auth();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public DMS.Model.dms_auth DataRowToModel(DataRow row)
        {
            DMS.Model.dms_auth model = new DMS.Model.dms_auth();
            if (row != null)
            {
                if (row["Id"] != null && row["Id"].ToString() != "")
                {
                    model.Id = new Guid(row["Id"].ToString());
                }
                if (row["ColumnId"] != null && row["ColumnId"].ToString() != "")
                {
                    model.ColumnId = int.Parse(row["ColumnId"].ToString());
                }
                if (row["ColumnPath"] != null)
                {
                    model.ColumnPath = row["ColumnPath"].ToString();
                }
                if (row["DirectoryId"] != null && row["DirectoryId"].ToString() != "")
                {
                    model.DirectoryId = int.Parse(row["DirectoryId"].ToString());
                }
                if (row["DirectoryPath"] != null)
                {
                    model.DirectoryPath = row["DirectoryPath"].ToString();
                }
                if (row["Auth"] != null)
                {
                    model.Auth = row["Auth"].ToString();
                }
                if (row["UserId"] != null && row["UserId"].ToString() != "")
                {
                    model.UserId = new Guid(row["UserId"].ToString());
                }
                if (row["Creator"] != null && row["Creator"].ToString() != "")
                {
                    model.Creator = new Guid(row["Creator"].ToString());
                }
                if (row["CreateTime"] != null && row["CreateTime"].ToString() != "")
                {
                    model.CreateTime = DateTime.Parse(row["CreateTime"].ToString());
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select Id,ColumnId,ColumnPath,DirectoryId,DirectoryPath,Auth,UserId,Creator,CreateTime ");
            strSql.Append(" FROM dms_auth ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" Id,ColumnId,ColumnPath,DirectoryId,DirectoryPath,Auth,UserId,Creator,CreateTime ");
            strSql.Append(" FROM dms_auth ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM dms_auth ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = DbHelperSQL.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.Id desc");
            }
            strSql.Append(")AS Row, T.*  from dms_auth T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return DbHelperSQL.Query(strSql.ToString());
        }

        /*
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetList(int PageSize,int PageIndex,string strWhere)
        {
            SqlParameter[] parameters = {
                    new SqlParameter("@tblName", SqlDbType.VarChar, 255),
                    new SqlParameter("@fldName", SqlDbType.VarChar, 255),
                    new SqlParameter("@PageSize", SqlDbType.Int),
                    new SqlParameter("@PageIndex", SqlDbType.Int),
                    new SqlParameter("@IsReCount", SqlDbType.Bit),
                    new SqlParameter("@OrderType", SqlDbType.Bit),
                    new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
                    };
            parameters[0].Value = "dms_auth";
            parameters[1].Value = "Id";
            parameters[2].Value = PageSize;
            parameters[3].Value = PageIndex;
            parameters[4].Value = 0;
            parameters[5].Value = 0;
            parameters[6].Value = strWhere;	
            return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
        }*/

        #endregion  BasicMethod
        #region  ExtensionMethod

        /// <summary>
        /// 获取用户在指定目录上的权限，根据路径反向查找
        /// </summary>
        /// <param name="userId">用户id</param>
        /// <param name="directoryPath">目录路径</param>
        /// <returns></returns>
        public DMS.Model.dms_auth GetModel(Guid userId, string directoryPath)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 Id,ColumnId,ColumnPath,DirectoryId,DirectoryPath,Auth,UserId,Creator,CreateTime from dms_auth ");
            strSql.Append(" where UserId=@UserId and @DirectoryPath+'|' like DirectoryPath+'|%' order by DirectoryPath");
            SqlParameter[] parameters = {
					new SqlParameter("@UserId", SqlDbType.UniqueIdentifier,16),
                    new SqlParameter("@DirectoryPath", SqlDbType.NVarChar,100)};
            parameters[0].Value = userId;
            parameters[1].Value = directoryPath;

            DMS.Model.dms_auth model = new DMS.Model.dms_auth();
            DataSet ds = DbHelperSQL.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }

        #endregion  ExtensionMethod
    }
}

