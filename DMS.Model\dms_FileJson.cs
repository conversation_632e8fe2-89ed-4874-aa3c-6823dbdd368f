﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace DMS.Model
{
    public class dms_FileJson
    {
        /// <summary>
        /// 编号
        /// </summary>
        public string Id { get; set; }
        /// <summary>
        /// 文件名称
        /// </summary>
        public string FileName { get; set; }

        /// <summary>
        /// 文件类型
        /// </summary>
        public string FileType { get; set; }

        /// <summary>
        /// 文件大小
        /// </summary>
        public string FileSize { get; set; }

        /// <summary>
        /// 文件路径
        /// </summary>
        public string FilePath { get; set; }

        /// <summary>
        /// 是否是文件
        /// </summary>
        public int IsFile { get; set; }

        /// <summary>
        /// 权限
        /// </summary>
        public string Auth { get; set; }

        /// <summary>
        /// 上传时间
        /// </summary>
        public string CreateTime { get; set; }

        /// <summary>
        /// 是否公开
        /// </summary>
        public bool IsOpen { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Memo { get; set; }
    }
}
