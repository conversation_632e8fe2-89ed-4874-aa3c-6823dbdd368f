﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YunEdu.Common;

namespace DMS.BLL
{
    public class CommonEnum
    {
        #region 文件类型
        public enum dms_FileType
        {
            /// <summary>
            /// txt文档
            /// </summary>
            [EnumDescription("txt文档")]
            Txt = 1,
            /// <summary>
            /// Word文档
            /// </summary>
            [EnumDescription("word文档")]
            Word = 2,
            /// <summary>
            /// Execl文档
            /// </summary>
            [EnumDescription("execl文档")]
            Excel = 3,
            /// <summary>
            /// ppt演示文档
            /// </summary>
            [EnumDescription("ppt演示文档")]
            Ppt = 4,
            /// <summary>
            /// 图片(JPG、PNG、PDF、TIFF、SWF等)
            /// </summary>
            [EnumDescription("图片文件")]
            Image = 5,
            /// <summary>
            /// 视频(AVI、RMVB、MP4、MID等)
            /// </summary>
            [EnumDescription("视频文件")]
            Video = 6,
            /// <summary>
            /// 音频(WMA、MP3等)
            /// </summary>
            [EnumDescription("音频文件")]
            Voice = 7,
            /// <summary>
            /// 压缩文件(RAR)
            /// </summary>
            [EnumDescription("压缩文件")]
            Rar = 8,
            /// <summary>
            /// 其他文件
            /// </summary>
            [EnumDescription("其他文件")]
            Others = 0,

        }
        public static string Getdms_FileType(string Value)
        {
            dms_FileType _status = (dms_FileType)Enum.Parse(typeof(dms_FileType), Value);
            return EnumDescription.GetDescription(_status);
        }
        #endregion

        #region 文件状态
        public enum dms_FileStatus
        {
            /// <summary>
            /// 上传中
            /// </summary>
            [EnumDescription("上传中")]
            Uploading = 1,
            /// <summary>
            /// 上传完成
            /// </summary>
            [EnumDescription("上传完成")]
            Completed = 2
        }

        public static string Getdms_FileStatus(string Value)
        {
            dms_FileStatus _status = (dms_FileStatus)Enum.Parse(typeof(dms_FileStatus), Value);
            return EnumDescription.GetDescription(_status);
        }
        #endregion
    }
}
