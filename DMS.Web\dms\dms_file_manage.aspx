﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="dms_file_manage.aspx.cs" Inherits="DMS.Web.dms.dms_file_manage" StylesheetTheme="Admin_Default" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>文档管理</title>
    <script src="/js/jquery-1.8.3.min.js" type="text/javascript"></script>
    <link href="/js/layer/skin/layer.css" rel="stylesheet" />
    <script src="/js/layer/layer.js" type="text/javascript"></script>
    <script src="/js/My97DatePicker/My97DatePicker/WdatePicker.js" type="text/javascript"></script>
    <script src="/admin/js/Common.js"></script>
    <style>
        #gvFileList .file-p { display: flex; justify-content: flex-start; align-items: center; }
            #gvFileList .file-p.pointer { cursor: pointer; }
        #gvFileList .file-icon { display: inline-block; width: 26px; height: 26px; position: relative; margin: 0 5px; }
            #gvFileList .file-icon.folder { background: url(images/folder.png) center no-repeat; }
            #gvFileList .file-icon.file { background: url(images/file.png) center no-repeat; }
        #gvFileList .action { margin-right: 5px; }
        #gvFileList .empty { padding-left: 10px; }
        .div_path { text-align: left; padding-left: 3px; }
            .div_path span { padding: 0 3px; }
        .hidden { display: none; }
        /*重命名*/
        .edit-filename { text-align: center; display: none; margin-top: 10px; }
            .edit-filename input { width: 240px; }
    </style>
</head>
<body>
    <form id="form1" runat="server">
        <div id="container">
            <asp:Panel ID="pnlButtons" runat="server" CssClass="search-bar">
                <span>查找：</span>
                <asp:TextBox ID="txtKeywords" runat="server" placeholder="文件名"></asp:TextBox>
                <span>日期：</span>
                <asp:TextBox ID="txtStart" runat="server" Width="80" class="Wdate" onClick="WdatePicker({maxDate:'#F{$dp.$D(\'txtEnd\',{d:0})}',dateFmt:'yyyy-MM-dd'})"></asp:TextBox>
                <span>-</span>
                <asp:TextBox ID="txtEnd" runat="server" Width="80" class="Wdate" onClick="WdatePicker({minDate:'#F{$dp.$D(\'txtStart\',{d:0})}',dateFmt:'yyyy-MM-dd'})"></asp:TextBox>
                <input id="btnSearch" type="button" value="查询" class="btn" />
                <input id="btnAddDirectory" type="button" value="新建文件夹" class="btnGreen" style="display: none;" />
                <input id="btnUpload" type="button" value="上传文件" class="btnGreen" style="display: none;" />
            </asp:Panel>
            <table class="content-bar stable" cellspacing="0" cellpadding="4" rules="all" border="1" id="gvFileList">
                <tbody>
                </tbody>
            </table>
            <div class="edit-filename">
                <div>
                    <input type="text" id="txtName" /><span></span>
                </div>
            </div>
        </div>
    </form>
    <script>
        var menuId = '<%=Request.QueryString["MenuId"]%>';
    </script>
    <script src="js/dms_file_manage.js"></script>
</body>
</html>
