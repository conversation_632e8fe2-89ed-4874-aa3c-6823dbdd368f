﻿/*!
 * CSS jQuery.filer
 * Copyright (c) 2015 CreativeDream
 * Version: 1.0.4 (29-Oct-2015)
*/
@import url('jquery-filer.css');

/*-------------------------
	Basic configurations
-------------------------*/
.jFiler * {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}

.jFiler {
    font-family: sans-serif;
    font-size: 14px;
    color: #494949;
}

/* Helpers */
.jFiler ul.list-inline li {
    display: inline-block;
    padding-right: 5px;
    padding-left: 5px;
}

.jFiler .pull-left {
    float: left;
}

.jFiler .pull-right {
    float: right;
}

/* File Icons */
span.jFiler-icon-file {
    position: relative;
    width: 57px;
    height: 70px;
    display: inline-block;
    line-height: 70px;
    text-align: center;
    border-radius: 3px;
    color: #fff;
    font-family: sans-serif;
    font-size: 13px;
    font-weight: bold;
    overflow: hidden;
    box-shadow: 42px -55px 0 0 #A4A7AC inset;
}

span.jFiler-icon-file:after {
    position: absolute;
    top: -1px;
    right: -1px;
    display: inline-block;
    content: '';
    border-style: solid;
    border-width: 16px 0 0 16px;
    border-color: transparent transparent transparent #DADDE1;
}

span.jFiler-icon-file i[class*="icon-jfi-"] {
    font-size: 24px;
}

span.jFiler-icon-file.f-image {
    box-shadow: 42px -55px 0 0 #e15955 inset;
}

span.jFiler-icon-file.f-image:after {
    border-left-color: #c6393f;
}

span.jFiler-icon-file.f-video {
    box-shadow: 42px -55px 0 0 #4183d7 inset;
}

span.jFiler-icon-file.f-video:after {
    border-left-color: #446cb3;
}

span.jFiler-icon-file.f-audio {
    box-shadow: 42px -55px 0 0 #5bab6e inset;
}

span.jFiler-icon-file.f-audio:after {
    border-left-color: #448353;
}


/* Progress Bar */
.jFiler-jProgressBar {
    height: 8px;
    background: #f1f1f1;
    margin-top: 3px;
    margin-bottom: 0;
    overflow: hidden;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;
}

.jFiler-jProgressBar .bar {
    float: left;
    width: 0;
    height: 100%;
    font-size: 12px;
    color: #ffffff;
    text-align: center;
    text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
    background-color: #50A1E9;
    box-sizing: border-box;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;
    -webkit-transition: width 0.3s ease;
    -moz-transition: width 0.3s ease;
    -o-transition: width 0.3s ease;
    transition: width 0.3s ease;
}

.jFiler-jProgressBar .bar.dark {
    background-color: #555;
}

.jFiler-jProgressBar .bar.blue {
    background-color: #428bca;
}

.jFiler-jProgressBar .bar.green {
    background-color: #5cb85c;
}

.jFiler-jProgressBar .bar.orange {
    background-color: #f7a923;
}

.jFiler-jProgressBar .bar.red {
    background-color: #d9534f;
}

/* Thumbs */
.jFiler-row:after,
.jFiler-item:after {
    display: table;
    line-height: 0;
    content: "";
    clear: both;
}

.jFiler-items ul {
    margin: 0;
    padding: 0;
    list-style: none;
}

/*-------------------------
	Default Theme
-------------------------*/
.jFiler-theme-default .jFiler-input {
    position: relative;
    display: block;
    width: 400px;
    height: 35px;
    margin: 0 0 15px 0;
    background: #fefefe;
    border: 1px solid #cecece;
    font-size: 12px;
    font-family: sans-serif;
    color: #888;
    border-radius: 4px;
    cursor: pointer;
    overflow: hidden;
    -webkit-box-shadow: rgba(0,0,0,.25) 0 4px 5px -5px inset;
       -moz-box-shadow: rgba(0,0,0,.25) 0 4px 5px -5px inset;
            box-shadow: rgba(0,0,0,.25) 0 4px 5px -5px inset;
}

.jFiler-theme-default .jFiler-input.focused {
    outline: none;
    -webkit-box-shadow: 0 0 7px rgba(0,0,0,0.1);
    -moz-box-shadow: 0 0 7px rgba(0,0,0,0.1);
    box-shadow: 0 0 7px rgba(0,0,0,0.1);
}

.jFiler-theme-default .jFiler.dragged .jFiler-input {
    border: 1px dashed #aaaaaa;
    background: #f9f9f9;
}

.jFiler-theme-default .jFiler.dragged .jFiler-input:hover {
    background: #FFF8D0;
}

.jFiler-theme-default .jFiler.dragged .jFiler-input * {
    pointer-events: none;
}

.jFiler-theme-default .jFiler.dragged .jFiler-input .jFiler-input-caption {
    width: 100%;
    text-align: center;
}

.jFiler-theme-default .jFiler.dragged .jFiler-input .jFiler-input-button {
    display: none;
}

.jFiler-theme-default .jFiler-input-caption {
    display: block;
    float: left;
    height: 100%;
    padding-top: 8px;
    padding-left: 10px;
    text-overflow: ellipsis;
    overflow: hidden;
}

.jFiler-theme-default .jFiler-input-button {
    display: block;
    float: right;
    height: 100%;
    padding-top: 8px;
    padding-left: 15px;
    padding-right: 15px;
    border-left: 1px solid #ccc;
    color: #666666;
    text-align: center;
    background-color: #fefefe;
    background-image: -webkit-gradient(linear,0 0,0 100%,from(#fefefe),to(#f1f1f1));
    background-image: -webkit-linear-gradient(top,#fefefe,#f1f1f1);
    background-image: -o-linear-gradient(top,#fefefe,#f1f1f1);
    background-image: linear-gradient(to bottom,#fefefe,#f1f1f1);
    background-image: -moz-linear-gradient(top,#fefefe,#f1f1f1);
    -webkit-transition: all .1s ease-out;
       -moz-transition: all .1s ease-out;
         -o-transition: all .1s ease-out;
            transition: all .1s ease-out;
}

.jFiler-theme-default .jFiler-input-button:hover {
    -moz-box-shadow: inset 0 0 10px rgba(0,0,0,0.07);
    -webkit-box-shadow: inset 0 0 10px rgba(0,0,0,0.07);
    box-shadow: inset 0 0 10px rgba(0,0,0,0.07);
}

.jFiler-theme-default .jFiler-input-button:active {
    background-image: -webkit-gradient(linear,0 0,0 100%,from(#f1f1f1),to(#fefefe));
    background-image: -webkit-linear-gradient(top,#f1f1f1,#fefefe);
    background-image: -o-linear-gradient(top,#f1f1f1,#fefefe);
    background-image: linear-gradient(to bottom,#f1f1f1,#fefefe);
    background-image: -moz-linear-gradient(top,#f1f1f1,#fefefe);
}

/*-------------------------
	Thumbnails
-------------------------*/
.jFiler-items-default .jFiler-items {
    
}

.jFiler-items-default .jFiler-item {
    position: relative;
    padding: 16px;
    margin-bottom: 16px;
    background: #f7f7f7;
    color: #4d4d4c;
}


.jFiler-items-default .jFiler-item .jFiler-item-icon {
    font-size: 32px;
    color: #f5871f;
    
    margin-right: 15px;
    margin-top: -3px;
}

.jFiler-items-default .jFiler-item .jFiler-item-title {
    font-weight: bold;
}

.jFiler-items-default .jFiler-item .jFiler-item-others {
    font-size: 12px;
    color: #777;
    margin-left: -5px;
    margin-right: -5px;
}

.jFiler-items-default .jFiler-item .jFiler-item-others span {
    padding-left: 5px;
    padding-right: 5px;
}

.jFiler-items-default .jFiler-item-assets {
    position: absolute;
    display: block;
    right: 16px;
    top: 50%;
    margin-top: -10px;
}

.jFiler-items-default .jFiler-item-assets a {
    padding: 8px 9px 8px 12px;
    cursor: pointer;
    background: #fafafa;
    color: #777;
    border-radius: 4px;
    border: 1px solid #e3e3e3
}

.jFiler-items-default .jFiler-item-assets .jFiler-item-trash-action:hover,
.jFiler-items-default .jFiler-item-assets .jFiler-item-trash-action:active {
    color: #d9534f;
}

.jFiler-items-default .jFiler-item-assets .jFiler-item-trash-action:active {
    background: transparent;
}

/* Thumbnails: Grid */
.jFiler-items-grid .jFiler-item {
    float: left;
}

.jFiler-items-grid .jFiler-item .jFiler-item-container {
    position: relative;
    margin: 0 20px 30px 0;
    padding: 10px;
    border: 1px solid #e1e1e1;
    border-radius: 3px;
    background: #fff;
    -webkit-box-shadow: 0px 0px 3px rgba(0,0,0,0.06);
    -moz-box-shadow: 0px 0px 3px rgba(0,0,0,0.06);
    box-shadow: 0px 0px 3px rgba(0,0,0,0.06);
}

.jFiler-items-grid .jFiler-item .jFiler-item-container .jFiler-item-thumb {
    position: relative;
    width: 160px;
    height: 115px;
    min-height: 115px;
    border: 1px solid #e1e1e1;
    overflow: hidden;
}

.jFiler-items-grid .jFiler-item .jFiler-item-container .jFiler-item-thumb .jFiler-item-thumb-image {
    width: 100%;
    height: 100%;
    text-align: center;
}

.jFiler-item .jFiler-item-container .jFiler-item-thumb img {
    max-width: none;
    max-height: 100%;
}

.jFiler-items-grid .jFiler-item .jFiler-item-container .jFiler-item-thumb span.jFiler-icon-file {
    margin-top: 20px;
}

.jFiler-items-grid .jFiler-item-thumb-image.fi-loading {
    ,
    width: 100%;
    height: 100%;
}

.jFiler-items-grid .jFiler-item .jFiler-item-container .jFiler-item-info {
    position: absolute;
    bottom: -10%;
    left: 0;
    width: 100%;
    color: #fff;
    padding: 6px 10px;
    background: -moz-linear-gradient(bottom,rgba(0,0,0,1) 0,rgba(0,0,0,0) 100%);
    background: -webkit-linear-gradient(bottom,rgba(0,0,0,1) 0,rgba(0,0,0,0) 100%);
    background: -o-linear-gradient(bottom,rgba(0,0,0,1) 0,rgba(0,0,0,0) 100%);
    background: -ms-linear-gradient(bottom,rgba(0,0,0,1) 0,rgba(0,0,0,0) 100%);
    background: linear-gradient(to top,rgba(0,0,0,1) 0,rgba(0,0,0,0) 100%);
    z-index: 9;
    opacity: 0;
    filter: alpha(opacity(0));
    -webkit-transition: all 0.12s;
    -moz-transition: all 0.12s;
    transition: all 0.12s;
}

.jFiler-items-grid .jFiler-no-thumbnail.jFiler-item .jFiler-item-container .jFiler-item-info {
    background: rgba(0,0,0,0.55);
}

.jFiler-items-grid .jFiler-item .jFiler-item-container .jFiler-item-thumb:hover .jFiler-item-info {
    bottom: 0;
    opacity: 1;
    filter: aplpha(opacity(100));
}

.jFiler-items-grid .jFiler-item .jFiler-item-container .jFiler-item-info .jFiler-item-title {
    display: block;
    font-weight: bold;
    word-break: break-all;
    line-height: 1;
}

.jFiler-items-grid .jFiler-item .jFiler-item-container .jFiler-item-info .jFiler-item-others {
    display: inline-block;
    font-size: 10px;
}

.jFiler-items-grid .jFiler-item .jFiler-item-container .jFiler-item-assets {
    margin-top: 10px;
    color: #999;
}

.jFiler-items-grid .jFiler-item .jFiler-item-container .jFiler-item-assets .text-success {
    color: #3C763D
}

.jFiler-items-grid .jFiler-items-grid .jFiler-item .jFiler-item-container .jFiler-item-assets .text-error {
    color: #A94442
}

.jFiler-items-grid .jFiler-item .jFiler-item-container .jFiler-item-assets .jFiler-jProgressBar {
    width: 120px;
    margin-left: -5px;
}

.jFiler-items-grid .jFiler-item .jFiler-item-container .jFiler-item-assets .jFiler-item-others {
    font-size: 12px;
}

.jFiler-items-grid .jFiler-item-trash-action:hover {
    cursor: pointer;
    color: #d9534f;
}