﻿using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using YunEdu.DBUtility;
using System.Collections.Generic;//Please add references
namespace DMS.BLL
{
	/// <summary>
	/// 数据访问类:JC_Config
	/// </summary>
	public partial class JC_Config
	{
		public JC_Config()
		{}
		#region  BasicMethod

		/// <summary>
		/// 得到最大ID
		/// </summary>
		public int GetMaxId()
		{
		return DbHelperSQL.GetMaxID("AreaId", "JC_Config"); 
		}

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(int AreaId,string DomainName)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) from JC_Config");
			strSql.Append(" where AreaId=@AreaId and DomainName=@DomainName ");
			SqlParameter[] parameters = {
					new SqlParameter("@AreaId", SqlDbType.Int,4),
					new SqlParameter("@DomainName", SqlDbType.NVarChar,256)			};
			parameters[0].Value = AreaId;
			parameters[1].Value = DomainName;

			return DbHelperSQL.Exists(strSql.ToString(),parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(DMS.Model.JC_Config model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into JC_Config(");
			strSql.Append("ID,AreaId,DomainName,IsEnabled,RegisterCode,Indate,IsEnabledModifyPwd,AreaPath,SystemName,IsEnableWelcome,UseVersion,AuthResult,ApiUrl,SchoolIds,FaceModule,HKFace,BaiduFace,UploadFileSizeLimit,UploadFileTypeLimit)");
			strSql.Append(" values (");
			strSql.Append("@ID,@AreaId,@DomainName,@IsEnabled,@RegisterCode,@Indate,@IsEnabledModifyPwd,@AreaPath,@SystemName,@IsEnableWelcome,@UseVersion,@AuthResult,@ApiUrl,@SchoolIds,@FaceModule,@HKFace,@BaiduFace,@UploadFileSizeLimit,@UploadFileTypeLimit)");
			SqlParameter[] parameters = {
					new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@AreaId", SqlDbType.Int,4),
					new SqlParameter("@DomainName", SqlDbType.NVarChar,256),
					new SqlParameter("@IsEnabled", SqlDbType.Bit,1),
					new SqlParameter("@RegisterCode", SqlDbType.NVarChar,500),
					new SqlParameter("@Indate", SqlDbType.NVarChar,256),
					new SqlParameter("@IsEnabledModifyPwd", SqlDbType.Bit,1),
					new SqlParameter("@AreaPath", SqlDbType.NVarChar,100),
					new SqlParameter("@SystemName", SqlDbType.NVarChar,256),
					new SqlParameter("@IsEnableWelcome", SqlDbType.Bit,1),
					new SqlParameter("@UseVersion", SqlDbType.NVarChar,50),
					new SqlParameter("@AuthResult", SqlDbType.NVarChar,-1),
					new SqlParameter("@ApiUrl", SqlDbType.NVarChar,256),
					new SqlParameter("@SchoolIds", SqlDbType.NVarChar,-1),
					new SqlParameter("@FaceModule", SqlDbType.Int,4),
					new SqlParameter("@HKFace", SqlDbType.NVarChar,-1),
					new SqlParameter("@BaiduFace", SqlDbType.NVarChar,-1),
					new SqlParameter("@UploadFileSizeLimit", SqlDbType.Int,4),
					new SqlParameter("@UploadFileTypeLimit", SqlDbType.NVarChar,100)};
			parameters[0].Value = Guid.NewGuid();
			parameters[1].Value = model.AreaId;
			parameters[2].Value = model.DomainName;
			parameters[3].Value = model.IsEnabled;
			parameters[4].Value = model.RegisterCode;
			parameters[5].Value = model.Indate;
			parameters[6].Value = model.IsEnabledModifyPwd;
			parameters[7].Value = model.AreaPath;
			parameters[8].Value = model.SystemName;
			parameters[9].Value = model.IsEnableWelcome;
			parameters[10].Value = model.UseVersion;
			parameters[11].Value = model.AuthResult;
			parameters[12].Value = model.ApiUrl;
			parameters[13].Value = model.SchoolIds;
			parameters[14].Value = model.FaceModule;
			parameters[15].Value = model.HKFace;
			parameters[16].Value = model.BaiduFace;
			parameters[17].Value = model.UploadFileSizeLimit;
			parameters[18].Value = model.UploadFileTypeLimit;

			int rows=DbHelperSQL.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(DMS.Model.JC_Config model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update JC_Config set ");
			strSql.Append("ID=@ID,");
			strSql.Append("IsEnabled=@IsEnabled,");
			strSql.Append("RegisterCode=@RegisterCode,");
			strSql.Append("Indate=@Indate,");
			strSql.Append("IsEnabledModifyPwd=@IsEnabledModifyPwd,");
			strSql.Append("AreaPath=@AreaPath,");
			strSql.Append("SystemName=@SystemName,");
			strSql.Append("IsEnableWelcome=@IsEnableWelcome,");
			strSql.Append("UseVersion=@UseVersion,");
			strSql.Append("AuthResult=@AuthResult,");
			strSql.Append("ApiUrl=@ApiUrl,");
			strSql.Append("SchoolIds=@SchoolIds,");
			strSql.Append("FaceModule=@FaceModule,");
			strSql.Append("HKFace=@HKFace,");
			strSql.Append("BaiduFace=@BaiduFace,");
			strSql.Append("UploadFileSizeLimit=@UploadFileSizeLimit,");
			strSql.Append("UploadFileTypeLimit=@UploadFileTypeLimit");
			strSql.Append(" where AreaId=@AreaId and DomainName=@DomainName ");
			SqlParameter[] parameters = {
					new SqlParameter("@ID", SqlDbType.UniqueIdentifier,16),
					new SqlParameter("@IsEnabled", SqlDbType.Bit,1),
					new SqlParameter("@RegisterCode", SqlDbType.NVarChar,500),
					new SqlParameter("@Indate", SqlDbType.NVarChar,256),
					new SqlParameter("@IsEnabledModifyPwd", SqlDbType.Bit,1),
					new SqlParameter("@AreaPath", SqlDbType.NVarChar,100),
					new SqlParameter("@SystemName", SqlDbType.NVarChar,256),
					new SqlParameter("@IsEnableWelcome", SqlDbType.Bit,1),
					new SqlParameter("@UseVersion", SqlDbType.NVarChar,50),
					new SqlParameter("@AuthResult", SqlDbType.NVarChar,-1),
					new SqlParameter("@ApiUrl", SqlDbType.NVarChar,256),
					new SqlParameter("@SchoolIds", SqlDbType.NVarChar,-1),
					new SqlParameter("@FaceModule", SqlDbType.Int,4),
					new SqlParameter("@HKFace", SqlDbType.NVarChar,-1),
					new SqlParameter("@BaiduFace", SqlDbType.NVarChar,-1),
					new SqlParameter("@UploadFileSizeLimit", SqlDbType.Int,4),
					new SqlParameter("@UploadFileTypeLimit", SqlDbType.NVarChar,100),
					new SqlParameter("@AreaId", SqlDbType.Int,4),
					new SqlParameter("@DomainName", SqlDbType.NVarChar,256)};
			parameters[0].Value = model.ID;
			parameters[1].Value = model.IsEnabled;
			parameters[2].Value = model.RegisterCode;
			parameters[3].Value = model.Indate;
			parameters[4].Value = model.IsEnabledModifyPwd;
			parameters[5].Value = model.AreaPath;
			parameters[6].Value = model.SystemName;
			parameters[7].Value = model.IsEnableWelcome;
			parameters[8].Value = model.UseVersion;
			parameters[9].Value = model.AuthResult;
			parameters[10].Value = model.ApiUrl;
			parameters[11].Value = model.SchoolIds;
			parameters[12].Value = model.FaceModule;
			parameters[13].Value = model.HKFace;
			parameters[14].Value = model.BaiduFace;
			parameters[15].Value = model.UploadFileSizeLimit;
			parameters[16].Value = model.UploadFileTypeLimit;
			parameters[17].Value = model.AreaId;
			parameters[18].Value = model.DomainName;

			int rows=DbHelperSQL.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(int AreaId,string DomainName)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from JC_Config ");
			strSql.Append(" where AreaId=@AreaId and DomainName=@DomainName ");
			SqlParameter[] parameters = {
					new SqlParameter("@AreaId", SqlDbType.Int,4),
					new SqlParameter("@DomainName", SqlDbType.NVarChar,256)			};
			parameters[0].Value = AreaId;
			parameters[1].Value = DomainName;

			int rows=DbHelperSQL.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public DMS.Model.JC_Config GetModel(int AreaId,string DomainName)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select  top 1 ID,AreaId,DomainName,IsEnabled,RegisterCode,Indate,IsEnabledModifyPwd,AreaPath,SystemName,IsEnableWelcome,UseVersion,AuthResult,ApiUrl,SchoolIds,FaceModule,HKFace,BaiduFace,UploadFileSizeLimit,UploadFileTypeLimit from JC_Config ");
			strSql.Append(" where AreaId=@AreaId and DomainName=@DomainName ");
			SqlParameter[] parameters = {
					new SqlParameter("@AreaId", SqlDbType.Int,4),
					new SqlParameter("@DomainName", SqlDbType.NVarChar,256)			};
			parameters[0].Value = AreaId;
			parameters[1].Value = DomainName;

			DMS.Model.JC_Config model=new DMS.Model.JC_Config();
			DataSet ds=DbHelperSQL.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public DMS.Model.JC_Config DataRowToModel(DataRow row)
		{
			DMS.Model.JC_Config model=new DMS.Model.JC_Config();
			if (row != null)
			{
				if(row["ID"]!=null && row["ID"].ToString()!="")
				{
					model.ID= new Guid(row["ID"].ToString());
				}
				if(row["AreaId"]!=null && row["AreaId"].ToString()!="")
				{
					model.AreaId=int.Parse(row["AreaId"].ToString());
				}
				if(row["DomainName"]!=null)
				{
					model.DomainName=row["DomainName"].ToString();
				}
				if(row["IsEnabled"]!=null && row["IsEnabled"].ToString()!="")
				{
					if((row["IsEnabled"].ToString()=="1")||(row["IsEnabled"].ToString().ToLower()=="true"))
					{
						model.IsEnabled=true;
					}
					else
					{
						model.IsEnabled=false;
					}
				}
				if(row["RegisterCode"]!=null)
				{
					model.RegisterCode=row["RegisterCode"].ToString();
				}
				if(row["Indate"]!=null)
				{
					model.Indate=row["Indate"].ToString();
				}
				if(row["IsEnabledModifyPwd"]!=null && row["IsEnabledModifyPwd"].ToString()!="")
				{
					if((row["IsEnabledModifyPwd"].ToString()=="1")||(row["IsEnabledModifyPwd"].ToString().ToLower()=="true"))
					{
						model.IsEnabledModifyPwd=true;
					}
					else
					{
						model.IsEnabledModifyPwd=false;
					}
				}
				if(row["AreaPath"]!=null)
				{
					model.AreaPath=row["AreaPath"].ToString();
				}
				if(row["SystemName"]!=null)
				{
					model.SystemName=row["SystemName"].ToString();
				}
				if(row["IsEnableWelcome"]!=null && row["IsEnableWelcome"].ToString()!="")
				{
					if((row["IsEnableWelcome"].ToString()=="1")||(row["IsEnableWelcome"].ToString().ToLower()=="true"))
					{
						model.IsEnableWelcome=true;
					}
					else
					{
						model.IsEnableWelcome=false;
					}
				}
				if(row["UseVersion"]!=null)
				{
					model.UseVersion=row["UseVersion"].ToString();
				}
				if(row["AuthResult"]!=null)
				{
					model.AuthResult=row["AuthResult"].ToString();
				}
				if(row["ApiUrl"]!=null)
				{
					model.ApiUrl=row["ApiUrl"].ToString();
				}
				if(row["SchoolIds"]!=null)
				{
					model.SchoolIds=row["SchoolIds"].ToString();
				}
				if(row["FaceModule"]!=null && row["FaceModule"].ToString()!="")
				{
					model.FaceModule=int.Parse(row["FaceModule"].ToString());
				}
				if(row["HKFace"]!=null)
				{
					model.HKFace=row["HKFace"].ToString();
				}
				if(row["BaiduFace"]!=null)
				{
					model.BaiduFace=row["BaiduFace"].ToString();
				}
				if(row["UploadFileSizeLimit"]!=null && row["UploadFileSizeLimit"].ToString()!="")
				{
					model.UploadFileSizeLimit=int.Parse(row["UploadFileSizeLimit"].ToString());
				}
				if(row["UploadFileTypeLimit"]!=null)
				{
					model.UploadFileTypeLimit=row["UploadFileTypeLimit"].ToString();
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ID,AreaId,DomainName,IsEnabled,RegisterCode,Indate,IsEnabledModifyPwd,AreaPath,SystemName,IsEnableWelcome,UseVersion,AuthResult,ApiUrl,SchoolIds,FaceModule,HKFace,BaiduFace,UploadFileSizeLimit,UploadFileTypeLimit ");
			strSql.Append(" FROM JC_Config ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			return DbHelperSQL.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
			strSql.Append(" ID,AreaId,DomainName,IsEnabled,RegisterCode,Indate,IsEnabledModifyPwd,AreaPath,SystemName,IsEnableWelcome,UseVersion,AuthResult,ApiUrl,SchoolIds,FaceModule,HKFace,BaiduFace,UploadFileSizeLimit,UploadFileTypeLimit ");
			strSql.Append(" FROM JC_Config ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return DbHelperSQL.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM JC_Config ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			object obj = DbHelperSQL.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T.DomainName desc");
			}
			strSql.Append(")AS Row, T.*  from JC_Config T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return DbHelperSQL.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "JC_Config";
			parameters[1].Value = "DomainName";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

