﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="dms_file_Auth.aspx.cs" Inherits="DMS.Web.dms.dms_file_Auth" StylesheetTheme="Admin_Default" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <script src="/js/jquery-1.8.3.min.js"></script>
    <script src="/js/layer/layer.js" type="text/javascript"></script>
    <script src="/admin/js/Common.js"></script>
    <title></title>
    <style>
        /*部门样式*/
        .formtitle {
            border-bottom: solid 1px #d0dee5;
            line-height: 35px;
            position: relative;
            height: 35px;
            margin-bottom: 22px;
            top: 0px;
            left: 0px;
        }

        .formtitle span {
            font-weight: bold;
            font-size: 14px;
            border-bottom: solid 3px #66c9f3;
            float: left;
            bottom: -1px;
            padding: 0 3px;
            height: 35px;
            line-height: 30px;
        }

        ul {
            font-size: 12px;
            list-style-type: none;
            margin: 0px;
            padding: 0px;
            display: inline-block;
        }

        ul li {
            float: left;
            border: 1px solid #B2B5C3;
            display: inline-block;
            margin: 0px 10px 10px 0px;
            text-align: center;
            padding: 6px 6px;
            display: inline-block;
            cursor: pointer;
        }
        /*表格样式*/
        .table td, .table th {
            font-size: 14px;
            height: 35px;
            text-align: center
        }

        input[type="checkbox"] {
            width: 16px;
            height: 16px
        }

        .div_path {
            display: none;
            font-size: 14px
        }
        .d-flex{
            display: flex;
            align-items: center;
            justify-content: center;
        }
    </style>
    
</head>
<body>
    <form id="form1" runat="server">
        <div id="container">

            <table style="width: 100%;" class="margin-bottom-bar">
                <tr>
                    <td>
                        <div class="formtitle">
                            <span class="div_name">部门</span><div class="div_path"></div>
                        </div>
                        <ul id="ul_group">
                        </ul>
                    </td>
                </tr>
                <tr>
                    <td>
                        <div id="dv_user"></div>
                    </td>
                </tr>
            </table>
            <div class="bottom-bar">
                <input id="btnAdd" type="button" value="保存" class="btnGreen" />
                <input id="btnClose" type="button" value="关闭" class="btnYellow" onclick="CloseBox()" />
            </div>
        </div>
    </form>
    <script>
        var id = "";
        $(function () {
            id = '<%= Request.QueryString["directoryId"] %>';
            var back = $('<a ></a>').html(" | 返回上一级").attr("href", "javascript:;").click(function () { backLevel(); });
            var all = $('<a ></a>').html("所有部门").attr("href", "javascript:;").click(function () { backToAll(); });
            $('.div_path').append(all, back);

            getGroup();
            //获取子级部门
            $("body").on("click", "#ul_group li", function () {
                var groupid = $(this).attr("groupid");
                var groupname = $(this).html();
                $("#selAll").attr("groupid", groupid);
                $("#selAll").attr("groupname", groupname);
                var groupInfo = { groupid: groupid, groupname: groupname }
                pushPath(groupInfo);
                getGroup(groupid);
            })
        })
        //获取部门
        function getGroup(groupid) {
            $.ajax({
                url: "ajax/uploadfiles.ashx?Method=getgroup&id=" + groupid,
                type: "post",
                cache: false,
                dataType: "json",
                success: function (data) {
                    $('#ul_group').html("");
                    if (data && data.length > 0) {
                        for (var i = 0; i < data.length; i++) {
                            $('#ul_group').append("<li class='tag' groupid='" + data[i].ID + "'>" + data[i].DeptName + "</li>")
                        }
                    }
                    getUserId(groupid, id);
                }
            })
        }
        //获取该部门下人员
        function getUserId(groupId, directoryid) {
            $.ajax({
                url: "ajax/uploadfiles.ashx?Method=getuser",
                data: {
                    "groupid": groupId,
                    "directoryid": directoryid,
                },
                cache: false,
                dataType: "json",
                success: function (data) {
                    if ($('#ul_group').html() == "") {
                        $(".formtitle").css('marginBottom', 0);
                    } else {
                        $(".formtitle").css('marginBottom', 22);
                    }
                    //当第二阶级存在部门但不存在用户则不显示暂无数据
                    if ($('#ul_group').html() != "" && !data.code) {
                        $('#dv_user').html("");
                    } else {
                        $('#dv_user').html(data.msg);
                    }

                }
            })
        }

        // 添加导航路径
        var path = [];
        var tempPath = [];
        function pushPath(node) {
            var index = path.length;
            var pathNode = $('<a></a>').addClass('path').html(' > ' + node.groupname).attr({ "href": "javascript:;" }).click(function () {
                popPath(index, node);
            });
            path.push(pathNode);
            path["node_" + index] = node;
            tempPath = $.extend([], path);
            $('.div_name').hide();
            $('.div_path').append(pathNode).show();
        }
        // 移除指定级别下的路径
        function popPath(index) {
            for (var i = path.length - 1; i > index; i--) {
                path[i].remove();
                tempPath.pop();
                delete tempPath["node_" + i];
            }
            path = $.extend([], tempPath);
            var groupid = 0;
            if (path.length > 0) {
                groupid = path["node_" + (path.length - 1)].groupid;
            }
            getGroup(groupid);
        }
        // 返回上一级
        function backLevel() {
            if (path.length > 1) {
                popPath(path.length - 2);
            }
            else {
                backToAll();
            }
        }
        // 返回全部文件
        function backToAll() {
            $('.path').remove();
            path = [];
            tempPath = [];
            $('.div_path').hide();
            $('.div_name').show();
            getGroup(0);
        }

        function CloseBox() {
            parent.layer.close(parent.layer.getFrameIndex(window.name));
        };
        function showMessage(success, content, ico) {
            var _index = parent.layer.alert(content, {
                icon: ico,
                skin: 'layer-ext-moon',
                shade: ['0.1', '#000'],
                time: success == "true" ? 1000 : 0,
                end: function () {
                    if (success == "true") {
                        CloseBox();
                    }

                }
            });
        }
        function setAllChecked(obj, index) {
            $('#atuhInfo tbody tr').find('td:eq(' + index + ') input[type=checkbox]').prop('checked', obj.checked);
        }
    </script>
    <script>
        //设置用户权限

        $("#btnAdd").click(function () {
            var authInfo = new Array();
            $("#atuhInfo").find("tr").not(":eq(0)").each(function () {
                var _auth = "";
                var tdArr = $(this).children();
                var userId = tdArr.eq(0).find("span").attr("userId");//用户Id
                var addAuth = tdArr.eq(1).find("input").attr("checked");//添加
                var editAuth = tdArr.eq(2).find("input").attr("checked");//编辑
                var delAuth = tdArr.eq(3).find("input").attr("checked");//删除
                if (addAuth == "checked") {
                    _auth += "1|"
                }
                if (editAuth == "checked") {
                    _auth += "2|"
                }
                if (delAuth == "checked") {
                    _auth += "3|"
                }
                if (userId != "") {
                    _auth = _auth.substring(0, _auth.length - 1);
                    authInfo.push({
                        UserId: userId,
                        Auth: _auth
                    })
                }
            });
            if (authInfo.length > 0) {
                $.ajax({
                    type: "post",
                    url: "ajax/uploadfiles.ashx",
                    data: { Method: 'saveUserAuth', directoryId: id, AuthInfo: JSON.stringify(authInfo) },
                    dataType: "json",
                    success: function (result) {
                        if (result.msg != "") { layer.msg(result.msg); } else {
                            layer.msg("配置失败！");
                        }
                    }
                });
            }
        })

    </script>
</body>
</html>

