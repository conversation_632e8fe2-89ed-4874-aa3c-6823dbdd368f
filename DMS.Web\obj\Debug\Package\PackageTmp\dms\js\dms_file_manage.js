﻿var rootId = 0;
var searching = false;
// 获取数据
function getDirectoryAndFiles(parentId) {
    $.ajax({
        type: 'get',
        url: "ajax/dms_file_manage.ashx",
        data: {
            "action": "getDirectoryAndFiles",
            "parentId": parentId,
            "menuId": menuId
        },
        dataType: 'json',
        success: function (result) {
            if (parentId == 0) {
                rootId = result.RootId;
            }
            //console.log(JSON.stringify(result))
            // 做权限处理
            // 创建
            if (result.UserAuth.indexOf("4") > -1) {
                $('#btnAddDirectory').show();
            }
            else {
                $('#btnAddDirectory').hide();
            }
            // 上传
            if (result.UserAuth.indexOf("1") > -1) {
                $('#btnUpload').show();
                $('#btnBatchUpload').show();
            }
            else {
                $('#btnUpload').hide();
                $('#btnBatchUpload').hide();
            }
            var table = $('#gvFileList tbody');
            table.find('tr:not(:first-child)').remove();
            if (result.FileList.length > 0) {
                $.each(result.FileList, function (i, n) {
                    table.append(createRow(n));
                })
            }
            else {
                table.append(createEmpty());
            }

        },
        error: function () {

        }
    });
}
// 下载
function fileDownload(id) {
    window.open("dms_download.aspx?id=" + id);
}
// 重命名
function Rename(id, name, isfile) {
    //是否是文件
    if (isfile && name.indexOf('.') > 0) {
        //截取后缀名
        name = name.substring(0, name.lastIndexOf('.'));
    }
    var filename = $(".edit-filename #txtName").val(name);
    layer.open({
        type: 1,
        title: "重命名",
        area: ['300px', '150px'],
        content: $(".edit-filename"),
        btn: ['更新'],
        yes: function (index) {
            var _filename = $(".edit-filename #txtName").val();
            var reg = /[\\/:*?"<>|]/;
            if (!reg.test(_filename) && $.trim(_filename) != "") {
                $.ajax({
                    type: "POST",
                    url: "ajax/dms_file_manage.ashx?action=" + (isfile ? "updateFileName" : "updateDirectoryName"),
                    data: { id: id, name: _filename },
                    dataType: "text",
                    success: function (response) {
                        if (response == "1") {
                            if (searching) {
                                $('#btnSearch').click();
                            }
                            else {
                                refreshData();
                            }
                        }
                        else {
                            var msg;
                            switch (response) {
                                case "4":
                                    msg = "文件/目录名已存在！";
                                    break;
                                case "5":
                                    msg = "操作FTP服务器失败！";
                                    break;
                                default:
                                    msg = "更新失败！";
                                    break;
                            }
                            layer.alert(msg, { icon: 2, title: '提示' });
                        }
                    }
                });
                layer.close(index);
            }
            else if ($.trim(_filename) == "") {
                layer.tips('必须输入文件名', ".edit-filename #txtName", { tips: [1, "#3595CC"] });
            }
            else {
                $(".edit-filename #txtName").keyup();
            }
        }
    });
}
// 删除操作
function delDirectory(ids) {
    layer.confirm("请确定删除选中的目录吗？", { title: '提示' }, function () {
        layer.confirm("警告删除不可恢复！请确定是否删除？", { title: '提示' }, function () {
            if (ids.length > 0) {
                del(ids, "");
            }
        });
    });
}
function delFiles(ids) {
    layer.confirm("请确定删除选中的文件吗？", { title: '提示' }, function () {
        layer.confirm("警告删除不可恢复！请确定是否删除？", { title: '提示' }, function () {
            if (ids.length > 0) {
                del("", ids);
            }
        });
    });
}
function delDirectoryOrFiles(directorys, files) {
    layer.confirm("请确定删除选中的文件或目录吗？", { title: '提示' }, function () {
        layer.confirm("警告删除不可恢复！请确定是否删除？", { title: '提示' }, function () {
            if (directorys.length > 0 || files.length > 0) {
                del(directorys, files);
            }
        });
    });
}
// 添加目录
function addDirectory(parentId, name) {
    $.ajax({
        type: "post",
        url: "ajax/dms_file_manage.ashx?action=addDirectory",
        data: { "name": name, "parentId": parentId },
        dataType: "text",
        success: function (result) {
            if (result == "1") {
                //var index = layer.alert('创建成功！', { icon: 1, title: '提示' }, function () { refreshData(); layer.close(index); })
                refreshData();
            }
            else {
                layer.alert('创建失败！', { icon: 2, title: '提示' });
            }
            searching = false;
        }
    });
}
// 搜寻
function search(parentId, keyword, start, end) {
    $.ajax({
        type: 'get',
        url: "ajax/dms_file_manage.ashx",
        data: {
            "action": "searchFiles",
            "parentId": parentId,
            "keyword": keyword,
            "start": start,
            "end": end
        },
        dataType: 'json',
        success: function (result) {
            var table = $('#gvFileList tbody');
            table.find('tr:not(:first-child)').remove();
            if (result.length > 0) {
                $.each(result, function (i, n) {
                    table.append(createRow(n));
                })
            }
            else {
                table.append(createEmpty());
            }
        }
    });
}
// 删除
function del(directorys, files) {
    $.ajax({
        type: 'get',
        url: "ajax/dms_file_manage.ashx",
        data: {
            "action": "delDirectoryOrFiles",
            "directorys": directorys,
            "files": files
        },
        dataType: 'json',
        success: function (result) {
            if (result.code) {
                var index = layer.alert('删除成功！', { icon: 1, title: '提示' }, function () { refreshData(); layer.close(index); })
            }
            else {
                layer.alert('请选择一条记录操作！', { icon: 3, title: '提示' })
            }
        },
        error: function () {
            layer.alert('删除失败！', { icon: 2, title: '提示' });
        }
    });
}
// 编辑
function edit(id) {
    layer.open({
        type: 2,
        title: "上传文件",
        area: ['700px', '700px'],
        content: 'dms_file_edit.aspx?id=' + id,
        end: function () {
            //refreshData();
        }
    });
};
// 公开文件
function setOpen(id) {
    var index = layer.confirm("是否公开此文件？", { title: '提示' }, function () {
        if (id.length > 0) {
            setFileStatus(id, 1);
            layer.close(index);
        }
    });
}
// 不公开文件
function setUnOpen(id) {
    var index = layer.confirm("是否设置此文件为不公开？", { title: '提示' }, function () {
        if (id.length > 0) {
            setFileStatus(id, 0);
            layer.close(index);
        }
    });
}
function setFileStatus(id, status) {
    $.ajax({
        type: "POST",
        url: "ajax/dms_file_manage.ashx?action=setFileStatus",
        data: { id: id, status: status },
        dataType: "text",
        success: function (response) {
            if (response == "1") {
                if (searching) {
                    $('#btnSearch').click();
                }
                else {
                    refreshData();
                }
            }
        }
    });
}
// 授权
function directoryAuth(directoryId, directoryName) {
    layer.open({
        type: 2,
        title: directoryName + "权限配置",
        skin: 'layui-layer-rim',
        area: ['600px', '700px'],
        shift: 7,
        content: 'dms_file_Auth.aspx?directoryId=' + directoryId,
        end: function () {
            //refreshData();
        }
    });
}
var path = [];
var tempPath = [];
// 获取当前parentId
function getParentId() {
    var parentId;
    if (path.length > 0) {
        parentId = path["node_" + (path.length - 1)].Id
    }
    else {
        parentId = 0;
    }
    return parentId;
}
// 添加路径
function pushPath(node) {
    var index = path.length;
    var pathNode = createElement('a').addClass('path').html(' > ' + node.FileName).attr({ "href": "javascript:;" }).click(function () {
        popPath(index, node);
    });
    path.push(pathNode);
    path["node_" + index] = node;
    tempPath = $.extend([], path);
    //tempPath.push(pathNode);
    //tempPath["" + index + ""] = node;

    $('.div_name').hide();
    $('.div_path').append(pathNode).show();
}
// 移除指定级别下的路径
function popPath(index) {
    for (var i = path.length - 1; i > index; i--) {
        path[i].remove();
        tempPath.pop();
        delete tempPath["node_" + i];
    }
    path = $.extend([], tempPath);
    var parentId = 0;
    if (path.length > 0) {
        parentId = path["node_" + (path.length - 1)].Id;
    }
    getDirectoryAndFiles(parentId);
}
// 返回上一级
function backLevel() {
    searching = false;
    if (path.length > 1) {
        popPath(path.length - 2);
    }
    else {
        backToAll();
    }
}
// 返回全部文件
function backToAll() {
    searching = false;
    $('.path').remove();
    path = [];
    tempPath = [];
    $('.div_path').hide();
    $('.div_name').show();
    getDirectoryAndFiles(0);
}
// 刷新数据
function refreshData() {
    getDirectoryAndFiles(getParentId());
}
function createThead() {
    var tr = createElement('tr').attr("align", "center").addClass("sheader");
    // 选择列
    //var th1 = createElement('th').attr("style", "width: 40px;");
    //var chk = createElement('checkbox').attr("id", "chkAll");
    //th1.append(chk);
    // 名称列
    var th2 = createElement('th').attr("style", "width:60%;");
    var div_name = createElement('div').addClass('div_name').html("名称");
    var div_path = createElement('div').addClass('div_path').hide();
    var back = createElement('a').html("返回上一级").attr("href", "javascript:;").click(function () { backLevel(); });
    var span = createElement('span').html("|");
    var all = createElement('a').html("全部文件").attr("href", "javascript:;").click(function () { backToAll(); });
    div_path.append(back, span, all);
    th2.append(div_name, div_path);
    // 上传时间
    var th3 = createElement('th').html("日期");
    // 类型列
    var th4 = createElement('th').html("类型");
    // 大小列
    var th5 = createElement('th').html("大小");
    // 是否公开            
    var th6 = createElement('th').html("公开").attr({ "align": "center", "style": "width: 40px;" });
    // 操作列
    var th7 = createElement('th').html("操作");
    // 放进tr中
    tr.append(th2, th3, th4, th5, th6, th7);
    return tr;
}
function createEmpty() {
    var tr = createElement('tr')
    var td = createElement("td").attr("colspan", 6).addClass('empty').html("暂无数据");
    return tr.append(td);
}
function createRow(data) {
    var tr = createElement('tr').addClass('data srow');
    // 选择列
    //var td1 = createElement('td').attr("align", "center");
    //var chk = createElement('checkbox');
    //td1.append(chk);
    // 名称列
    var td2 = createElement('td');
    var ico = createElement('span').addClass('file-icon').addClass(data.IsFile == 1 ? "file" : "folder");
    var name = createElement('span').addClass('file-name').html(data.FileName);
    var div = createElement("div").addClass("file-p");
    if (data.IsFile == 0) {
        div.addClass("pointer").click(function () {
            var id = data.Id;
            //console.log(id);
            getDirectoryAndFiles(id);
            pushPath(data);
        });
    }
    else {
        div.attr("title", data.Memo);
    }
    div.append(ico, name)
    td2.append(div);
    // 上传时间
    var td3 = createElement('td');
    var time = createElement('span').addClass('file-upload-time').html(data.CreateTime);
    td3.append(time);
    // 类型列
    var td4 = createElement('td');
    var type = createElement('span').addClass('file-type').html(data.FileType);
    td4.append(type);
    // 大小列
    var td5 = createElement('td');
    var size = createElement('span').addClass('file-size').html(data.FileSize);
    td5.append(size);
    // 公开
    var td6 = createElement('td').attr("style", "text-align:center;");
    if (data.IsFile == 1) {
        var img = createElement('img').attr('src', '/admin/images/' + data.IsOpen.toString().toLowerCase() + ".gif");
        td6.append(img);
    }
    // 操作列
    var td7 = createElement('td');
    if (data.IsFile == 1) {
        var download = createElement('a').html("下载").attr("href", "javascript:;").addClass('action btnWhite light').click(function () { fileDownload(data.Id); });
        td7.append(download);
    }
    // 授权权限
    if (data.Auth.indexOf("4") > -1 && data.IsFile == 0) {
        var auth = createElement('a').html("权限配置").attr("href", "javascript:;").addClass('action btnGreen light').click(function () { directoryAuth(data.Id, data.FileName); });
        td7.append(auth)
    }
    // 编辑权限
    if (data.Auth.indexOf("2") > -1) {
        var renameAction = createElement('a').html("重命名").attr("href", "javascript:;").addClass('action btnWhite light').click(function () { Rename(data.Id, data.FileName, data.IsFile == 1); });
        td7.append(renameAction);
        if (data.IsFile == 1) {
            var editAction = createElement('a').html("编辑").attr("href", "javascript:;").addClass('action btn light').click(function () { edit(data.Id); });
            td7.append(editAction);
            // 公开操作按钮
            var openAction = createElement('a').html(data.IsOpen ? "不公开" : "公开").attr("href", "javascript:;").addClass('action btnWhite light').click(function () { data.IsOpen ? setUnOpen(data.Id) : setOpen(data.Id); });
            td7.append(openAction);
        }
    }
    // 删除权限
    if (data.Auth.indexOf("3") > -1) {
        var del = createElement('a').html("删除").attr("href", "javascript:;").addClass('action btnRed light').click(function () { data.IsFile == 1 ? delFiles(data.Id) : delDirectory(data.Id); });
        td7.append(del)
    }

    // 放进tr中
    tr.append(td2, td3, td4, td5, td6, td7);
    return tr;
}
function createElement(type) {
    switch (type) {
        case "a":
            return $('<a></a>');
            break;
        case "div":
            return $('<div></div>');
            break;
        case "span":
            return $('<span></span>');
            break;
        case "tr":
            return $('<tr></tr>');
            break;
        case "td":
            return $('<td></td>');
            break;
        case "th":
            return $('<th></th>');
            break;
        case "checkbox":
            return $('<input type="checkbox" />');
            break;
        case "img":
            return $('<img />');
            break;
        default:
            break;
    }
}
$(function () {
    $('#gvFileList tbody').append(createThead());
    getDirectoryAndFiles(0);
    $(".edit-filename #txtName").keyup(function () {
        var reg = /[\\/:*?"<>|]/;
        if (reg.test(this.value)) {
            this.value = this.value.replace(reg, '');
            layer.tips('文件名不能包含下列任何字符:<br/>\\ / : * ? " < > |', ".edit-filename #txtName", { tips: [1, "#3595CC"] });
        }
    });
    $('#btnSearch').click(function () {
        // 检索
        return false;
    });
    // 添加
    $('#btnAddDirectory').click(function () {
        $(".edit-filename #txtName").val('')
        layer.open({
            type: 1,
            title: "新建文件夹",
            area: ['300px', '150px'],
            content: $(".edit-filename"),
            btn: ['新建'],
            yes: function (index) {
                var _filename = $(".edit-filename #txtName").val();
                var reg = /[\\/:*?"<>|]/;
                if (!reg.test(_filename) && $.trim(_filename) != "") {
                    addDirectory(getParentId(), _filename);
                    layer.close(index);
                }
                else if ($.trim(_filename) == "") {
                    layer.tips('必须输入文件名', ".edit-filename #txtName", { tips: [1, "#3595CC"] });
                }
                else {
                    $(".edit-filename #txtName").keyup();
                }
            }
        });
        return false;
    });
    // 上传
    $('#btnUpload').click(function () {
        $(".edit-filename #txtName").val('');
        var directoryId = getParentId();
        if (directoryId == 0) {
            directoryId = rootId;
        }
        layer.open({
            type: 2,
            title: "上传文件",
            area: ['700px', '700px'],
            content: 'dms_file_Add.aspx?directoryId=' + directoryId,
            end: function () {
                //refreshData();
            }
        });
        return false;
    });
    // 查询
    $('#btnSearch').click(function () {
        $(".edit-filename #txtName").val('');
        $('.div_name').hide();
        $('.div_path').show();
        searching = true;
        search(getParentId(), $('#txtKeywords').val().trim(), $('#txtStart').val(), $('#txtEnd').val());
        return false;
    });

});