﻿using System.Web;

namespace DMS.Web.dms
{
    /// <summary>
    /// 上传文件信息
    /// </summary>
    public class UploadFileInfo
    {
        /// <summary>
        /// 文件名
        /// </summary>
        public string FileName { get; set; }

        /// <summary>
        /// 文件存储名
        /// </summary>
        public string SaveName { get; set; }

        /// <summary>
        /// 文件扩展名
        /// </summary>
        public string FileExt { get; set; }

        /// <summary>
        /// 文件长度
        /// </summary>
        public int FileSize { get; set; }

        /// <summary>
        /// 文件MD5值
        /// </summary>
        public string FileMD5 { get; set; }

        /// <summary>
        /// 文件在服务器的路径
        /// </summary>
        public string FilePath { get; set; }
    }
}