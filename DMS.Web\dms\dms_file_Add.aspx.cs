﻿using DMS.BLL;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using YunEdu.Common;
using YunEdu.Common.DEncrypt;

namespace DMS.Web.dms
{
    public partial class dms_file_Add : BasePage
    {
        public string ImgName = "";
        public string IsAdd = "1";
        Guid Id = Guid.Empty;
        int directoryId = 0;
        Model.dms_files modelfiles = new Model.dms_files();
        BLL.dms_files bllfiles = new BLL.dms_files();

        protected void Page_Load(object sender, EventArgs e)
        {
            if (!string.IsNullOrEmpty(Request.QueryString["Id"])) Guid.TryParse(Request.QueryString["Id"], out Id);
            if (!string.IsNullOrEmpty(Request.QueryString["directoryId"])) int.TryParse(Request.QueryString["directoryId"], out directoryId);
            if (!IsPostBack)
            {
                hidSchool.Value = modelAreaUser.ColumnID.ToString();
                hidDirectoryId.Value = directoryId.ToString();
                string _domain = DESEncrypt.Encrypt(Request.Url.Authority.ToLower(), CodeTable.EncryptKeySys);
                spanFileType1.InnerText = spanFileType2.InnerText = bllfiles.GetFileTypeLimit(modelAreaUser.ColumnID, _domain);
                hidFileType.Value = spanFileType1.InnerText.Replace("*.", "").Replace("，", "");
                hidFileSize.Value = bllfiles.GetFileSizeLimit(modelAreaUser.ColumnID, _domain);
                Show();
            }
        }

        private void Show()
        {
            modelfiles = bllfiles.GetModel(Id);
            //编辑
            if (Id != Guid.Empty && modelfiles != null)
            {
                IsAdd = "0";
                this.txtTitle.Text = modelfiles.FileName;
                this.txtContents.Text = modelfiles.Memo;
                //文件展示（size大小KB转换为B）
                List<WorksFileName> files = new List<WorksFileName>();
                if (!string.IsNullOrEmpty(modelfiles.FilePath))
                {
                    files.Add(new WorksFileName
                    {
                        ext = modelfiles.FileFormat,
                        md5 = modelfiles.FileMD5,
                        name = modelfiles.FileName,
                        path = modelfiles.FilePath,
                        size = modelfiles.FileSize * 1024,
                    });
                }
                this.hidSingleFile.Value = JsonConvert.SerializeObject(files);
                if (modelfiles.IsOpen == 1)
                {
                    this.chkIsOpen.Checked = true;
                }
                else
                {
                    this.chkIsOpen.Checked = false;
                }
            }
        }

        protected void btnSave_Click(object sender, EventArgs e)
        {
            //获取文件所在目录信息
            Model.dms_directory modelDirectory = new BLL.dms_directory().GetModel(directoryId);

            if (modelDirectory == null)
            {
                MessageBox.ResponseScript(this, "showMessage('false','当前目录不存在，请刷新页面！',0)");
                return;
            }
            Guid guidUserId = Guid.Parse(UserId);

            //判断用户是否有添加权限
            Model.dms_auth modelAuth = new BLL.dms_auth().GetModel(guidUserId, modelDirectory.Id);
            if (!(modelAuth != null && !string.IsNullOrEmpty(modelAuth.Auth) && modelAuth.Auth.IndexOf("1") > -1)
                && !RoleLevel.Contains(((int)CodeTable.AdminRoleLevel.SchoolAdmin).ToString()))
            {
                MessageBox.ResponseScript(this, "showMessage('false','您当前没有添加文件权限，请联系管理员！',0)");
                return;
            }

            if (hidUploadType.Value == "0")
            {
                //编辑
                if (Id != Guid.Empty)
                {
                    modelfiles = bllfiles.GetModel(Id);
                }
                else
                {
                    modelfiles = new Model.dms_files();
                    modelfiles.DirectoryId = modelDirectory.Id;
                    modelfiles.DirectoryPath = modelDirectory.Path;
                    modelfiles.ColumnId = modelAreaUser.ColumnID;
                    modelfiles.ColumnPath = modelAreaUser.ColumnPath;
                }

                List<WorksFileName> fileList = JsonConvert.DeserializeObject<List<WorksFileName>>(this.hidSingleFile.Value);
                if (fileList.Count > 0)
                {
                    foreach (WorksFileName list in fileList)
                    {
                        modelfiles.FilePath = list.path;
                        modelfiles.FileSize = int.Parse(Math.Ceiling(Convert.ToDecimal(list.size / 1024)).ToString());
                        modelfiles.FileFormat = list.ext;
                        modelfiles.FileType = bllfiles.getFileType(list.ext);
                        modelfiles.FileMD5 = list.md5;
                        modelfiles.FileStatus = (int)CommonEnum.dms_FileStatus.Completed;
                    }
                }

                string newFileName = this.txtTitle.Text.Contains("." + modelfiles.FileFormat) ? txtTitle.Text.Trim() : txtTitle.Text.Trim() + "." + modelfiles.FileFormat;
                modelfiles.FileName = newFileName;
                modelfiles.Memo = Regex.Replace(txtContents.Text, @"<(.[^>]*)>", string.Empty, RegexOptions.IgnoreCase);
                modelfiles.CreateTime = DateTime.Now;
                modelfiles.Creator = guidUserId;
                modelfiles.IsOpen = chkIsOpen.Checked ? 1 : 0;

                //编辑
                if (modelfiles.Id != Guid.Empty)
                {
                    bllfiles.Update(modelfiles);
                }
                else
                {
                    bllfiles.Add(modelfiles);
                }
            }
            else
            {
                List<WorksFileName> fileList = JsonConvert.DeserializeObject<List<WorksFileName>>(hidMultipleFile.Value);
                if (fileList.Count > 0)
                {
                    foreach (WorksFileName list in fileList)
                    {
                        var modelfiles = new Model.dms_files();
                        modelfiles.DirectoryId = modelDirectory.Id;
                        modelfiles.DirectoryPath = modelDirectory.Path;
                        modelfiles.ColumnId = modelDirectory.ColumnId;
                        modelfiles.ColumnPath = modelDirectory.ColumnPath;
                        modelfiles.FilePath = list.path;
                        modelfiles.FileSize = int.Parse(Math.Ceiling(Convert.ToDecimal(list.size / 1024)).ToString());
                        modelfiles.FileFormat = list.ext;
                        modelfiles.FileType = bllfiles.getFileType(list.ext);
                        modelfiles.FileMD5 = list.md5;
                        modelfiles.FileStatus = (int)CommonEnum.dms_FileStatus.Completed;
                        modelfiles.FileName = list.name;
                        modelfiles.Memo = "";
                        modelfiles.CreateTime = DateTime.Now;
                        modelfiles.Creator = guidUserId;
                        modelfiles.IsOpen = chkIsOpenMult.Checked ? 1 : 0;
                        bllfiles.Add(modelfiles);
                    }
                }
            }
            MessageBox.ResponseScript(this, "showMessage('true','保存成功！',1)");
        }
        public class WorksFileName
        {
            /// <summary>
            /// 文件路径
            /// </summary>
            public string path { get; set; }
            /// <summary>
            /// 文件大小
            /// </summary>
            public long size { get; set; }
            /// <summary>
            /// 文件扩展名
            /// </summary>
            public string ext { get; set; }
            /// <summary>
            /// 文件名
            /// </summary>
            public string name { get; set; }
            /// <summary>
            /// MD5
            /// </summary>
            public string md5 { get; set; }
        }
    }
}