using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using FluentFTP;
using FluentFTP.Exceptions;

namespace DMS.Web.dms
{
    /// <summary>
    /// FTP项目信息类
    /// </summary>
    public class FtpItemInfoFluentFTP
    {
        /// <summary>
        /// 项目名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 项目类型
        /// </summary>
        public FtpObjectType Type { get; set; }

        /// <summary>
        /// 项目类型描述
        /// </summary>
        public string TypeDescription
        {
            get
            {
                switch (Type)
                {
                    case FtpObjectType.Directory:
                        return "目录";
                    case FtpObjectType.File:
                        return "文件";
                    case FtpObjectType.Link:
                        return "链接";
                    default:
                        return "未知";
                }
            }
        }

        /// <summary>
        /// 文件大小（字节）
        /// </summary>
        public long Size { get; set; }

        /// <summary>
        /// 格式化的文件大小
        /// </summary>
        public string FormattedSize
        {
            get
            {
                if (Type == FtpObjectType.Directory)
                    return "-";

                if (Size < 1024)
                    return $"{Size} B";
                else if (Size < 1024 * 1024)
                    return $"{Size / 1024.0:F2} KB";
                else if (Size < 1024 * 1024 * 1024)
                    return $"{Size / (1024.0 * 1024.0):F2} MB";
                else
                    return $"{Size / (1024.0 * 1024.0 * 1024.0):F2} GB";
            }
        }

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime ModifyTime { get; set; }

        /// <summary>
        /// 权限信息
        /// </summary>
        public string Permissions { get; set; }

        /// <summary>
        /// 完整路径
        /// </summary>
        public string FullPath { get; set; }

        /// <summary>
        /// 从FtpListItem创建FtpItemInfoFluentFTP
        /// </summary>
        /// <param name="item">FtpListItem对象</param>
        /// <returns>FtpItemInfoFluentFTP对象</returns>
        public static FtpItemInfoFluentFTP FromFtpListItem(FtpListItem item)
        {
            return new FtpItemInfoFluentFTP
            {
                Name = item.Name,
                Type = item.Type,
                Size = item.Size,
                ModifyTime = item.Modified,
                Permissions = item.RawPermissions,
                FullPath = item.FullName
            };
        }
    }

    /// <summary>
    /// 基于FluentFTP的FTP操作工具类
    /// 支持文件夹和文件的基础操作，包含断点续传功能
    /// </summary>
    public class FtpHelperFluentFTP : IDisposable
    {
        #region 私有字段

        private readonly string _server;
        private readonly string _username;
        private readonly string _password;
        private readonly int _port;
        private bool _disposed = false;

        #endregion

        #region 公共属性

        /// <summary>
        /// 服务器地址
        /// </summary>
        public string Server => _server;

        /// <summary>
        /// 用户名
        /// </summary>
        public string Username => _username;

        /// <summary>
        /// 端口号
        /// </summary>
        public int Port => _port;

        #endregion

        #region 构造函数

        /// <summary>
        /// 初始化FTP帮助类
        /// </summary>
        /// <param name="server">FTP服务器地址</param>
        /// <param name="username">用户名</param>
        /// <param name="password">密码</param>
        /// <param name="port">端口号，默认21</param>
        public FtpHelperFluentFTP(string server, string username, string password, int port = 21)
        {
            if (string.IsNullOrWhiteSpace(server))
                throw new ArgumentException("服务器地址不能为空", nameof(server));
            if (string.IsNullOrWhiteSpace(username))
                throw new ArgumentException("用户名不能为空", nameof(username));
            if (string.IsNullOrWhiteSpace(password))
                throw new ArgumentException("密码不能为空", nameof(password));

            _server = server.Trim();
            _username = username.Trim();
            _password = password.Trim();
            _port = port;
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化FTP客户端
        /// </summary>
        private FtpClient CreateFtpClient()
        {
            var client = new FtpClient(_server, _username, _password, _port);

            // 配置客户端参数
            client.Config.ConnectTimeout = 30000; // 30秒连接超时
            client.Config.ReadTimeout = 30000;    // 30秒读取超时
            client.Config.DataConnectionType = FtpDataConnectionType.AutoPassive; // 自动被动模式
            client.Config.EncryptionMode = FtpEncryptionMode.None; // 不加密
            client.Config.RetryAttempts = 3; // 重试3次

            // 设置编码
            client.Encoding = System.Text.Encoding.UTF8;

            client.LegacyLogger = (level, msg) =>
            {
                Console.WriteLine(msg);
            };
            return client;
        }

        /// <summary>
        /// 初始化AsyncFtp客户端
        /// </summary>
        private AsyncFtpClient CreateAsyncFtpClient()
        {
            var client = new AsyncFtpClient(_server, _username, _password, _port);

            // 配置客户端参数
            client.Config.ConnectTimeout = 30000; // 30秒连接超时
            client.Config.ReadTimeout = 30000;    // 30秒读取超时
            client.Config.DataConnectionType = FtpDataConnectionType.AutoPassive; // 自动被动模式
            client.Config.EncryptionMode = FtpEncryptionMode.None; // 不加密
            client.Config.RetryAttempts = 3; // 重试3次

            // 设置编码
            client.Encoding = System.Text.Encoding.UTF8;
            return client;
        }

        /// <summary>
        /// 确保FTP连接
        /// </summary>
        private void EnsureConnected(FtpClient client)
        {
            if (client == null)
                throw new ObjectDisposedException(nameof(FtpHelperFluentFTP));

            if (!client.IsConnected)
            {
                client.AutoConnect();
            }
        }

        /// <summary>
        /// 异步确保FTP连接
        /// </summary>
        private async Task EnsureConnectedAsync(AsyncFtpClient client)
        {
            if (client == null)
                throw new ObjectDisposedException(nameof(FtpHelperFluentFTP));

            if (!client.IsConnected)
            {
                await client.AutoConnect();
            }
        }

        #endregion

        #region 错误处理

        /// <summary>
        /// 获取详细的错误信息
        /// </summary>
        /// <param name="ex">异常对象</param>
        /// <returns>详细错误信息</returns>
        public static string GetDetailedErrorMessage(Exception ex)
        {
            if (ex == null) return "未知错误";

            string message = ex.Message;

            var ftpEx = ex as FtpCommandException;
            if (ftpEx != null)
            {
                message = $"FTP错误: {ftpEx.Message}";
                if (ftpEx.CompletionCode != null)
                {
                    message += $" (代码: {ftpEx.CompletionCode})";
                }
            }
            else if (ex is TimeoutException)
            {
                message = "操作超时，请检查网络连接或增加超时时间";
            }
            else if (ex is UnauthorizedAccessException)
            {
                message = "访问被拒绝，请检查用户名和密码";
            }
            else if (ex is DirectoryNotFoundException)
            {
                message = "目录不存在";
            }
            else if (ex is FileNotFoundException)
            {
                message = "文件不存在";
            }

            if (ex.InnerException != null)
            {
                message += $" 内部错误: {ex.InnerException.Message}";
            }

            return message;
        }

        #endregion

        #region 连接和基础操作

        /// <summary>
        /// 测试FTP连接
        /// </summary>
        /// <returns>连接是否成功</returns>
        public bool TestConnection()
        {
            try
            {
                using (var client = CreateFtpClient())
                {
                    EnsureConnected(client);
                    return client.IsConnected;
                }
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 异步测试FTP连接
        /// </summary>
        /// <returns>连接是否成功</returns>
        public async Task<bool> TestConnectionAsync()
        {
            try
            {
                using (var client = CreateAsyncFtpClient())
                {
                    await EnsureConnectedAsync(client);
                    return client.IsConnected;
                }
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 获取FTP服务器系统类型
        /// </summary>
        /// <returns>系统类型</returns>
        public string GetSystemType()
        {
            try
            {
                using (var client = CreateFtpClient())
                {
                    EnsureConnected(client);
                    return client.SystemType ?? "Unknown";
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"获取系统类型失败: {GetDetailedErrorMessage(ex)}", ex);
            }
        }

        /// <summary>
        /// 异步获取FTP服务器系统类型
        /// </summary>
        /// <returns>系统类型</returns>
        public async Task<string> GetSystemTypeAsync()
        {
            try
            {
                using (var client = CreateAsyncFtpClient())
                {
                    await EnsureConnectedAsync(client);
                    return client.SystemType ?? "Unknown";
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"获取系统类型失败: {GetDetailedErrorMessage(ex)}", ex);
            }
        }

        /// <summary>
        /// 获取当前工作目录
        /// </summary>
        /// <returns>当前目录路径</returns>
        public string GetCurrentDirectory()
        {
            try
            {
                using (var client = CreateFtpClient())
                {
                    EnsureConnected(client);
                    return client.GetWorkingDirectory();
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"获取当前目录失败: {GetDetailedErrorMessage(ex)}", ex);
            }
        }

        /// <summary>
        /// 异步获取当前工作目录
        /// </summary>
        /// <returns>当前目录路径</returns>
        public async Task<string> GetCurrentDirectoryAsync()
        {
            try
            {
                using (var client = CreateAsyncFtpClient())
                {
                    await EnsureConnectedAsync(client);
                    return await client.GetWorkingDirectory();
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"获取当前目录失败: {GetDetailedErrorMessage(ex)}", ex);
            }
        }

        /// <summary>
        /// 设置当前工作目录
        /// </summary>
        /// <param name="path">目录路径</param>
        /// <returns>是否成功</returns>
        public bool SetCurrentDirectory(string path)
        {
            try
            {
                using (var client = CreateFtpClient())
                {
                    EnsureConnected(client);
                    client.SetWorkingDirectory(path);
                    return true;
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"设置当前目录失败: {GetDetailedErrorMessage(ex)}", ex);
            }
        }

        /// <summary>
        /// 异步设置当前工作目录
        /// </summary>
        /// <param name="path">目录路径</param>
        /// <returns>是否成功</returns>
        public async Task<bool> SetCurrentDirectoryAsync(string path)
        {
            try
            {
                using (var client = CreateAsyncFtpClient())
                {
                    await EnsureConnectedAsync(client);
                    await client.SetWorkingDirectory(path);
                    return true;
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"设置当前目录失败: {GetDetailedErrorMessage(ex)}", ex);
            }
        }

        #endregion

        #region 文件夹操作

        /// <summary>
        /// 创建目录
        /// </summary>
        /// <param name="path">目录路径</param>
        /// <returns>是否成功</returns>
        public bool CreateDirectory(string path)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(path))
                    throw new ArgumentException("目录路径不能为空", nameof(path));

                using (var client = CreateFtpClient())
                {
                    EnsureConnected(client);
                    return client.CreateDirectory(path);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"创建目录失败: {GetDetailedErrorMessage(ex)}", ex);
            }
        }

        /// <summary>
        /// 异步创建目录
        /// </summary>
        /// <param name="path">目录路径</param>
        /// <returns>是否成功</returns>
        public async Task<bool> CreateDirectoryAsync(string path)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(path))
                    throw new ArgumentException("目录路径不能为空", nameof(path));

                using (var client = CreateAsyncFtpClient())
                {
                    await EnsureConnectedAsync(client);
                    return await client.CreateDirectory(path);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"创建目录失败: {GetDetailedErrorMessage(ex)}", ex);
            }
        }

        /// <summary>
        /// 删除目录
        /// </summary>
        /// <param name="path">目录路径</param>
        /// <returns>是否成功</returns>
        public bool DeleteDirectory(string path)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(path))
                    throw new ArgumentException("目录路径不能为空", nameof(path));

                using (var client = CreateFtpClient())
                {
                    EnsureConnected(client);
                    client.DeleteDirectory(path);
                    return true;
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"删除目录失败: {GetDetailedErrorMessage(ex)}", ex);
            }
        }

        /// <summary>
        /// 异步删除目录
        /// </summary>
        /// <param name="path">目录路径</param>
        /// <returns>是否成功</returns>
        public async Task<bool> DeleteDirectoryAsync(string path)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(path))
                    throw new ArgumentException("目录路径不能为空", nameof(path));
                using (var client = CreateAsyncFtpClient())
                {
                    await EnsureConnectedAsync(client);
                    await client.DeleteDirectory(path);
                    return true;
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"删除目录失败: {GetDetailedErrorMessage(ex)}", ex);
            }
        }

        /// <summary>
        /// 重命名目录
        /// </summary>
        /// <param name="oldPath">原目录路径</param>
        /// <param name="newPath">新目录路径</param>
        /// <returns>是否成功</returns>
        public bool RenameDirectory(string oldPath, string newPath)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(oldPath))
                    throw new ArgumentException("原目录路径不能为空", nameof(oldPath));
                if (string.IsNullOrWhiteSpace(newPath))
                    throw new ArgumentException("新目录路径不能为空", nameof(newPath));

                using (var client = CreateFtpClient())
                {
                    EnsureConnected(client);
                    client.Rename(oldPath, newPath);
                    return true;
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"重命名目录失败: {GetDetailedErrorMessage(ex)}", ex);
            }
        }

        /// <summary>
        /// 异步重命名目录
        /// </summary>
        /// <param name="oldPath">原目录路径</param>
        /// <param name="newPath">新目录路径</param>
        /// <returns>是否成功</returns>
        public async Task<bool> RenameDirectoryAsync(string oldPath, string newPath)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(oldPath))
                    throw new ArgumentException("原目录路径不能为空", nameof(oldPath));
                if (string.IsNullOrWhiteSpace(newPath))
                    throw new ArgumentException("新目录路径不能为空", nameof(newPath));

                using (var client = CreateAsyncFtpClient())
                {
                    await EnsureConnectedAsync(client);
                    await client.Rename(oldPath, newPath);
                    return true;
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"重命名目录失败: {GetDetailedErrorMessage(ex)}", ex);
            }
        }

        /// <summary>
        /// 检查目录是否存在
        /// </summary>
        /// <param name="path">目录路径</param>
        /// <returns>是否存在</returns>
        public bool DirectoryExists(string path)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(path))
                    return false;

                using (var client = CreateFtpClient())
                {
                    EnsureConnected(client);
                    return client.DirectoryExists(path);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"检查目录存在失败: {GetDetailedErrorMessage(ex)}", ex);
            }
        }

        /// <summary>
        /// 异步检查目录是否存在
        /// </summary>
        /// <param name="path">目录路径</param>
        /// <returns>是否存在</returns>
        public async Task<bool> DirectoryExistsAsync(string path)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(path))
                    return false;

                using (var client = CreateAsyncFtpClient())
                {
                    await EnsureConnectedAsync(client);
                    return await client.DirectoryExists(path);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"检查目录存在失败: {GetDetailedErrorMessage(ex)}", ex);
            }
        }

        /// <summary>
        /// 获取目录列表
        /// </summary>
        /// <param name="path">目录路径</param>
        /// <returns>目录名称列表</returns>
        public List<string> GetDirectoryList(string path = "/")
        {
            try
            {
                using (var client = CreateFtpClient())
                {
                    EnsureConnected(client);
                    var items = client.GetListing(path, FtpListOption.Modify);
                    return items.Where(x => x.Type == FtpObjectType.Directory).Select(x => x.Name).ToList();
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"获取目录列表失败: {GetDetailedErrorMessage(ex)}", ex);
            }
        }

        /// <summary>
        /// 异步获取目录列表
        /// </summary>
        /// <param name="path">目录路径</param>
        /// <returns>目录名称列表</returns>
        public async Task<List<string>> GetDirectoryListAsync(string path = "/")
        {
            try
            {
                using (var client = CreateAsyncFtpClient())
                {
                    await EnsureConnectedAsync(client);
                    var items = await client.GetListing(path, FtpListOption.Modify);
                    return items.Where(x => x.Type == FtpObjectType.Directory).Select(x => x.Name).ToList();
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"获取目录列表失败: {GetDetailedErrorMessage(ex)}", ex);
            }
        }

        /// <summary>
        /// 获取文件列表
        /// </summary>
        /// <param name="path">目录路径</param>
        /// <returns>文件名称列表</returns>
        public List<string> GetFileList(string path = "/")
        {
            try
            {
                using (var client = CreateFtpClient())
                {
                    EnsureConnected(client);
                    var items = client.GetListing(path, FtpListOption.Modify);
                    return items.Where(x => x.Type == FtpObjectType.File).Select(x => x.Name).ToList();
                }

            }
            catch (Exception ex)
            {
                throw new Exception($"获取文件列表失败: {GetDetailedErrorMessage(ex)}", ex);
            }
        }

        /// <summary>
        /// 异步获取文件列表
        /// </summary>
        /// <param name="path">目录路径</param>
        /// <returns>文件名称列表</returns>
        public async Task<List<string>> GetFileListAsync(string path = "/")
        {
            try
            {
                using (var client = CreateAsyncFtpClient())
                {
                    await EnsureConnectedAsync(client);
                    var items = await client.GetListing(path, FtpListOption.Modify);
                    return items.Where(x => x.Type == FtpObjectType.File).Select(x => x.Name).ToList();
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"获取文件列表失败: {GetDetailedErrorMessage(ex)}", ex);
            }
        }

        /// <summary>
        /// 获取详细的目录列表
        /// </summary>
        /// <param name="path">目录路径</param>
        /// <returns>详细项目信息列表</returns>
        public List<FtpItemInfoFluentFTP> GetDetailedDirectoryList(string path = "/")
        {
            try
            {
                using (var client = CreateFtpClient())
                {
                    EnsureConnected(client);
                    var items = client.GetListing(path, FtpListOption.Modify | FtpListOption.Size);
                    return items.Select(FtpItemInfoFluentFTP.FromFtpListItem).ToList();
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"获取详细目录列表失败: {GetDetailedErrorMessage(ex)}", ex);
            }
        }

        /// <summary>
        /// 异步获取详细的目录列表
        /// </summary>
        /// <param name="path">目录路径</param>
        /// <returns>详细项目信息列表</returns>
        public async Task<List<FtpItemInfoFluentFTP>> GetDetailedDirectoryListAsync(string path = "/")
        {
            try
            {
                using (var client = CreateAsyncFtpClient())
                {
                    await EnsureConnectedAsync(client);
                    var items = await client.GetListing(path, FtpListOption.Modify | FtpListOption.Size);
                    return items.Select(FtpItemInfoFluentFTP.FromFtpListItem).ToList();
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"获取详细目录列表失败: {GetDetailedErrorMessage(ex)}", ex);
            }
        }

        #endregion

        #region 文件基础操作

        /// <summary>
        /// 检查文件是否存在
        /// </summary>
        /// <param name="path">文件路径</param>
        /// <returns>是否存在</returns>
        public bool FileExists(string path)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(path))
                    return false;

                using (var client = CreateFtpClient())
                {
                    EnsureConnected(client);
                    return client.FileExists(path);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"检查文件存在失败: {GetDetailedErrorMessage(ex)}", ex);
            }
        }

        /// <summary>
        /// 异步检查文件是否存在
        /// </summary>
        /// <param name="path">文件路径</param>
        /// <returns>是否存在</returns>
        public async Task<bool> FileExistsAsync(string path)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(path))
                    return false;

                using (var client = CreateAsyncFtpClient())
                {
                    await EnsureConnectedAsync(client);
                    return await client.FileExists(path);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"检查文件存在失败: {GetDetailedErrorMessage(ex)}", ex);
            }
        }

        /// <summary>
        /// 获取文件大小
        /// </summary>
        /// <param name="path">文件路径</param>
        /// <returns>文件大小（字节），-1表示失败</returns>
        public long GetFileSize(string path)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(path))
                    return -1;

                using (var client = CreateFtpClient())
                {
                    EnsureConnected(client);
                    return client.GetFileSize(path);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"获取文件大小失败: {GetDetailedErrorMessage(ex)}", ex);
            }
        }

        /// <summary>
        /// 异步获取文件大小
        /// </summary>
        /// <param name="path">文件路径</param>
        /// <returns>文件大小（字节），-1表示失败</returns>
        public async Task<long> GetFileSizeAsync(string path)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(path))
                    return -1;

                using (var client = CreateAsyncFtpClient())
                {
                    await EnsureConnectedAsync(client);
                    return await client.GetFileSize(path);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"获取文件大小失败: {GetDetailedErrorMessage(ex)}", ex);
            }
        }

        /// <summary>
        /// 删除文件
        /// </summary>
        /// <param name="path">文件路径</param>
        /// <returns>是否成功</returns>
        public bool DeleteFile(string path)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(path))
                    throw new ArgumentException("文件路径不能为空", nameof(path));

                using (var client = CreateFtpClient())
                {
                    EnsureConnected(client);
                    client.DeleteFile(path);
                    return true;
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"删除文件失败: {GetDetailedErrorMessage(ex)}", ex);
            }
        }

        /// <summary>
        /// 异步删除文件
        /// </summary>
        /// <param name="path">文件路径</param>
        /// <returns>是否成功</returns>
        public async Task<bool> DeleteFileAsync(string path)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(path))
                    throw new ArgumentException("文件路径不能为空", nameof(path));

                using (var client = CreateAsyncFtpClient())
                {
                    await EnsureConnectedAsync(client);
                    await client.DeleteFile(path);
                    return true;
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"删除文件失败: {GetDetailedErrorMessage(ex)}", ex);
            }
        }

        /// <summary>
        /// 重命名文件
        /// </summary>
        /// <param name="oldPath">原文件路径</param>
        /// <param name="newPath">新文件路径</param>
        /// <returns>是否成功</returns>
        public bool RenameFile(string oldPath, string newPath)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(oldPath))
                    throw new ArgumentException("原文件路径不能为空", nameof(oldPath));
                if (string.IsNullOrWhiteSpace(newPath))
                    throw new ArgumentException("新文件路径不能为空", nameof(newPath));

                using (var client = CreateFtpClient())
                {
                    EnsureConnected(client);
                    client.Rename(oldPath, newPath);
                    return true;
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"重命名文件失败: {GetDetailedErrorMessage(ex)}", ex);
            }
        }

        /// <summary>
        /// 异步重命名文件
        /// </summary>
        /// <param name="oldPath">原文件路径</param>
        /// <param name="newPath">新文件路径</param>
        /// <returns>是否成功</returns>
        public async Task<bool> RenameFileAsync(string oldPath, string newPath)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(oldPath))
                    throw new ArgumentException("原文件路径不能为空", nameof(oldPath));
                if (string.IsNullOrWhiteSpace(newPath))
                    throw new ArgumentException("新文件路径不能为空", nameof(newPath));

                using (var client = CreateAsyncFtpClient())
                {
                    await EnsureConnectedAsync(client);
                    await client.Rename(oldPath, newPath);
                    return true;
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"重命名文件失败: {GetDetailedErrorMessage(ex)}", ex);
            }
        }

        #endregion

        #region 文件上传

        /// <summary>
        /// 上传文件
        /// </summary>
        /// <param name="localPath">本地文件路径</param>
        /// <param name="remotePath">远程文件路径</param>
        /// <param name="overwrite">是否覆盖</param>
        /// <returns>是否成功</returns>
        public bool UploadFile(string localPath, string remotePath, bool overwrite = true)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(localPath))
                    throw new ArgumentException("本地文件路径不能为空", nameof(localPath));
                if (string.IsNullOrWhiteSpace(remotePath))
                    throw new ArgumentException("远程文件路径不能为空", nameof(remotePath));
                if (!File.Exists(localPath))
                    throw new FileNotFoundException($"本地文件不存在: {localPath}");

                using (var client = CreateFtpClient())
                {
                    EnsureConnected(client);
                    var result = client.UploadFile(localPath, remotePath,
                    overwrite ? FtpRemoteExists.Overwrite : FtpRemoteExists.Skip);
                    return result == FtpStatus.Success;
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"上传文件失败: {GetDetailedErrorMessage(ex)}", ex);
            }
        }

        /// <summary>
        /// 异步上传文件
        /// </summary>
        /// <param name="localPath">本地文件路径</param>
        /// <param name="remotePath">远程文件路径</param>
        /// <param name="overwrite">是否覆盖</param>
        /// <returns>是否成功</returns>
        public async Task<bool> UploadFileAsync(string localPath, string remotePath, bool overwrite = true)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(localPath))
                    throw new ArgumentException("本地文件路径不能为空", nameof(localPath));
                if (string.IsNullOrWhiteSpace(remotePath))
                    throw new ArgumentException("远程文件路径不能为空", nameof(remotePath));
                if (!File.Exists(localPath))
                    throw new FileNotFoundException($"本地文件不存在: {localPath}");

                using (var client = CreateAsyncFtpClient())
                {
                    await EnsureConnectedAsync(client);
                    var result = await client.UploadFile(localPath, remotePath,
                       overwrite ? FtpRemoteExists.Overwrite : FtpRemoteExists.Skip);

                    return result == FtpStatus.Success;
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"上传文件失败: {GetDetailedErrorMessage(ex)}", ex);
            }
        }

        /// <summary>
        /// 带进度反馈的上传文件
        /// </summary>
        /// <param name="localPath">本地文件路径</param>
        /// <param name="remotePath">远程文件路径</param>
        /// <param name="progress">进度回调</param>
        /// <param name="overwrite">是否覆盖</param>
        /// <returns>是否成功</returns>
        public bool UploadFileWithProgress(string localPath, string remotePath,
            Action<FtpProgress> progress = null, bool overwrite = true)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(localPath))
                    throw new ArgumentException("本地文件路径不能为空", nameof(localPath));
                if (string.IsNullOrWhiteSpace(remotePath))
                    throw new ArgumentException("远程文件路径不能为空", nameof(remotePath));
                if (!File.Exists(localPath))
                    throw new FileNotFoundException($"本地文件不存在: {localPath}");

                using (var client = CreateFtpClient())
                {
                    EnsureConnected(client);
                    var result = client.UploadFile(localPath, remotePath, overwrite ? FtpRemoteExists.Overwrite : FtpRemoteExists.Skip, false, FtpVerify.None, progress);
                    return result == FtpStatus.Success;
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"上传文件失败: {GetDetailedErrorMessage(ex)}", ex);
            }
        }

        /// <summary>
        /// 异步带进度反馈的上传文件
        /// </summary>
        /// <param name="localPath">本地文件路径</param>
        /// <param name="remotePath">远程文件路径</param>
        /// <param name="progress">进度回调</param>
        /// <param name="overwrite">是否覆盖</param>
        /// <param name="token">取消令牌</param>
        /// <returns>是否成功</returns>
        public async Task<bool> UploadFileWithProgressAsync(string localPath, string remotePath,
            IProgress<FtpProgress> progress = null, bool overwrite = true, CancellationToken token = default(CancellationToken))
        {
            try
            {
                if (string.IsNullOrWhiteSpace(localPath))
                    throw new ArgumentException("本地文件路径不能为空", nameof(localPath));
                if (string.IsNullOrWhiteSpace(remotePath))
                    throw new ArgumentException("远程文件路径不能为空", nameof(remotePath));
                if (!File.Exists(localPath))
                    throw new FileNotFoundException($"本地文件不存在: {localPath}");

                using (var client = CreateAsyncFtpClient())
                {
                    await EnsureConnectedAsync(client);
                    var result = await client.UploadFile(localPath, remotePath, overwrite ? FtpRemoteExists.Overwrite : FtpRemoteExists.Skip, false, FtpVerify.None, progress, token);
                    return result == FtpStatus.Success;
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"上传文件失败: {GetDetailedErrorMessage(ex)}", ex);
            }
        }

        /// <summary>
        /// 断点续传上传文件
        /// </summary>
        /// <param name="localPath">本地文件路径</param>
        /// <param name="remotePath">远程文件路径</param>
        /// <param name="retryAttempts">重试次数</param>
        /// <param name="progress">进度回调</param>
        /// <returns>是否成功</returns>
        public bool UploadFileWithResume(string localPath, string remotePath, int retryAttempts = 3,
            Action<FtpProgress> progress = null)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(localPath))
                    throw new ArgumentException("本地文件路径不能为空", nameof(localPath));
                if (string.IsNullOrWhiteSpace(remotePath))
                    throw new ArgumentException("远程文件路径不能为空", nameof(remotePath));
                if (!File.Exists(localPath))
                    throw new FileNotFoundException($"本地文件不存在: {localPath}");

                using (var client = CreateFtpClient())
                {
                    EnsureConnected(client);
                    var result = client.UploadFile(localPath, remotePath, FtpRemoteExists.Resume, false, FtpVerify.None, progress);
                    return result == FtpStatus.Success;
                }
            }
            catch (Exception ex)
            {
                if (retryAttempts > 0)
                {
                    // 重试
                    Thread.Sleep(1000); // 等待1秒后重试
                    return UploadFileWithResume(localPath, remotePath, retryAttempts - 1, progress);
                }
                throw new Exception($"断点续传上传文件失败: {GetDetailedErrorMessage(ex)}", ex);
            }
        }

        /// <summary>
        /// 异步断点续传上传文件
        /// </summary>
        /// <param name="localPath">本地文件路径</param>
        /// <param name="remotePath">远程文件路径</param>
        /// <param name="retryAttempts">重试次数</param>
        /// <param name="progress">进度回调</param>
        /// <param name="token">取消令牌</param>
        /// <returns>是否成功</returns>
        public async Task<bool> UploadFileWithResumeAsync(string localPath, string remotePath,
            int retryAttempts = 3, IProgress<FtpProgress> progress = null, CancellationToken token = default(CancellationToken))
        {
            try
            {
                if (string.IsNullOrWhiteSpace(localPath))
                    throw new ArgumentException("本地文件路径不能为空", nameof(localPath));
                if (string.IsNullOrWhiteSpace(remotePath))
                    throw new ArgumentException("远程文件路径不能为空", nameof(remotePath));
                if (!File.Exists(localPath))
                    throw new FileNotFoundException($"本地文件不存在: {localPath}");

                using (var client = CreateAsyncFtpClient())
                {
                    await EnsureConnectedAsync(client);
                    var result = await client.UploadFile(localPath, remotePath, FtpRemoteExists.Resume, false, FtpVerify.None, progress, token);
                    return result == FtpStatus.Success;
                }
            }
            catch (Exception ex)
            {
                if (retryAttempts > 0 && !token.IsCancellationRequested)
                {
                    // 重试
                    await Task.Delay(1000, token); // 等待1秒后重试
                    return await UploadFileWithResumeAsync(localPath, remotePath, retryAttempts - 1, progress, token);
                }
                throw new Exception($"断点续传上传文件失败: {GetDetailedErrorMessage(ex)}", ex);
            }
        }

        /// <summary>
        /// 从流上传文件
        /// </summary>
        /// <param name="stream">文件流</param>
        /// <param name="remotePath">远程文件路径</param>
        /// <param name="overwrite">是否覆盖</param>
        /// <param name="progress">进度回调</param>
        /// <returns>是否成功</returns>
        public bool UploadFileFromStream(Stream stream, string remotePath, bool overwrite = true,
            Action<FtpProgress> progress = null)
        {
            try
            {
                if (stream == null)
                    throw new ArgumentNullException(nameof(stream));
                if (string.IsNullOrWhiteSpace(remotePath))
                    throw new ArgumentException("远程文件路径不能为空", nameof(remotePath));

                using (var client = CreateFtpClient())
                {
                    EnsureConnected(client);
                    var result = client.UploadStream(stream, remotePath, overwrite ? FtpRemoteExists.Overwrite : FtpRemoteExists.Skip, false, progress);
                    return result == FtpStatus.Success;
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"从流上传文件失败: {GetDetailedErrorMessage(ex)}", ex);
            }
        }

        /// <summary>
        /// 异步从流上传文件
        /// </summary>
        /// <param name="stream">文件流</param>
        /// <param name="remotePath">远程文件路径</param>
        /// <param name="overwrite">是否覆盖</param>
        /// <param name="progress">进度回调</param>
        /// <param name="token">取消令牌</param>
        /// <returns>是否成功</returns>
        public async Task<bool> UploadFileFromStreamAsync(Stream stream, string remotePath, bool overwrite = true,
            IProgress<FtpProgress> progress = null, CancellationToken token = default(CancellationToken))
        {
            try
            {
                if (stream == null)
                    throw new ArgumentNullException(nameof(stream));
                if (string.IsNullOrWhiteSpace(remotePath))
                    throw new ArgumentException("远程文件路径不能为空", nameof(remotePath));

                using (var client = CreateAsyncFtpClient())
                {
                    await EnsureConnectedAsync(client);

                    var result = await client.UploadStream(stream, remotePath,
                        overwrite ? FtpRemoteExists.Overwrite : FtpRemoteExists.Skip, false, progress, token);

                    return result == FtpStatus.Success;
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"从流上传文件失败: {GetDetailedErrorMessage(ex)}", ex);
            }
        }

        /// <summary>
        /// 从流上传文件（支持断点续传和失败重试）
        /// </summary>
        /// <param name="stream">文件流</param>
        /// <param name="remotePath">远程文件路径</param>
        /// <param name="retryAttempts">重试次数，默认3次</param>
        /// <param name="progress">进度回调</param>
        /// <returns>是否成功</returns>
        public bool UploadFileFromStreamWithResume(Stream stream, string remotePath, int retryAttempts = 3, Action<FtpProgress> progress = null)
        {
            try
            {
                if (stream == null)
                    throw new ArgumentNullException(nameof(stream));
                if (string.IsNullOrWhiteSpace(remotePath))
                    throw new ArgumentException("远程文件路径不能为空", nameof(remotePath));

                using (var client = CreateFtpClient())
                {
                    EnsureConnected(client);
                    var result = client.UploadStream(stream, remotePath, FtpRemoteExists.Resume, false, progress);
                    return result == FtpStatus.Success;
                }
            }
            catch (Exception ex)
            {
                if (retryAttempts > 0)
                {
                    // 重试
                    Thread.Sleep(1000); // 等待1秒后重试
                    return UploadFileFromStreamWithResume(stream, remotePath, retryAttempts - 1, progress);
                }
                throw new Exception($"从流上传文件失败: {GetDetailedErrorMessage(ex)}", ex);
            }
        }

        /// <summary>
        /// 异步从流上传文件（支持断点续传和失败重试）
        /// </summary>
        /// <param name="stream">文件流</param>
        /// <param name="remotePath">远程文件路径</param>
        /// <param name="retryAttempts">重试次数，默认3次</param>
        /// <param name="progress">进度回调</param>
        /// <param name="token">取消令牌</param>
        /// <returns>是否成功</returns>
        public async Task<bool> UploadFileFromStreamWithResumeAsync(Stream stream, string remotePath,
            int retryAttempts = 3, IProgress<FtpProgress> progress = null, CancellationToken token = default(CancellationToken))
        {
            try
            {
                if (stream == null)
                    throw new ArgumentNullException(nameof(stream));
                if (string.IsNullOrWhiteSpace(remotePath))
                    throw new ArgumentException("远程文件路径不能为空", nameof(remotePath));

                using (var client = CreateAsyncFtpClient())
                {
                    await EnsureConnectedAsync(client);
                    var result = await client.UploadStream(stream, remotePath, FtpRemoteExists.Resume, false, progress, token);
                    return result == FtpStatus.Success;
                }
            }
            catch (Exception ex)
            {
                if (retryAttempts > 0 && !token.IsCancellationRequested)
                {
                    // 重试
                    await Task.Delay(1000, token); // 等待1秒后重试
                    return await UploadFileFromStreamWithResumeAsync(stream, remotePath, retryAttempts - 1, progress, token);
                }
                throw new Exception($"从流上传文件失败: {GetDetailedErrorMessage(ex)}", ex);
            }
        }

        #endregion

        #region 文件下载

        /// <summary>
        /// 下载文件
        /// </summary>
        /// <param name="remotePath">远程文件路径</param>
        /// <param name="localPath">本地文件路径</param>
        /// <param name="overwrite">是否覆盖</param>
        /// <returns>是否成功</returns>
        public bool DownloadFile(string remotePath, string localPath, bool overwrite = true)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(remotePath))
                    throw new ArgumentException("远程文件路径不能为空", nameof(remotePath));
                if (string.IsNullOrWhiteSpace(localPath))
                    throw new ArgumentException("本地文件路径不能为空", nameof(localPath));

                using (var client = CreateFtpClient())
                {
                    EnsureConnected(client);

                    // 确保本地目录存在
                    var localDir = Path.GetDirectoryName(localPath);
                    if (!string.IsNullOrEmpty(localDir) && !Directory.Exists(localDir))
                    {
                        Directory.CreateDirectory(localDir);
                    }

                    var result = client.DownloadFile(localPath, remotePath,
                        overwrite ? FtpLocalExists.Overwrite : FtpLocalExists.Skip);

                    return result == FtpStatus.Success;
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"下载文件失败: {GetDetailedErrorMessage(ex)}", ex);
            }
        }

        /// <summary>
        /// 异步下载文件
        /// </summary>
        /// <param name="remotePath">远程文件路径</param>
        /// <param name="localPath">本地文件路径</param>
        /// <param name="overwrite">是否覆盖</param>
        /// <returns>是否成功</returns>
        public async Task<bool> DownloadFileAsync(string remotePath, string localPath, bool overwrite = true)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(remotePath))
                    throw new ArgumentException("远程文件路径不能为空", nameof(remotePath));
                if (string.IsNullOrWhiteSpace(localPath))
                    throw new ArgumentException("本地文件路径不能为空", nameof(localPath));

                using (var client = CreateAsyncFtpClient())
                {
                    await EnsureConnectedAsync(client);

                    // 确保本地目录存在
                    var localDir = Path.GetDirectoryName(localPath);
                    if (!string.IsNullOrEmpty(localDir) && !Directory.Exists(localDir))
                    {
                        Directory.CreateDirectory(localDir);
                    }

                    var result = await client.DownloadFile(localPath, remotePath,
                        overwrite ? FtpLocalExists.Overwrite : FtpLocalExists.Skip);

                    return result == FtpStatus.Success;
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"下载文件失败: {GetDetailedErrorMessage(ex)}", ex);
            }
        }

        /// <summary>
        /// 带进度反馈的下载文件
        /// </summary>
        /// <param name="remotePath">远程文件路径</param>
        /// <param name="localPath">本地文件路径</param>
        /// <param name="progress">进度回调</param>
        /// <param name="overwrite">是否覆盖</param>
        /// <returns>是否成功</returns>
        public bool DownloadFileWithProgress(string remotePath, string localPath,
            Action<FtpProgress> progress = null, bool overwrite = true)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(remotePath))
                    throw new ArgumentException("远程文件路径不能为空", nameof(remotePath));
                if (string.IsNullOrWhiteSpace(localPath))
                    throw new ArgumentException("本地文件路径不能为空", nameof(localPath));

                using (var client = CreateFtpClient())
                {
                    EnsureConnected(client);

                    // 确保本地目录存在
                    var localDir = Path.GetDirectoryName(localPath);
                    if (!string.IsNullOrEmpty(localDir) && !Directory.Exists(localDir))
                    {
                        Directory.CreateDirectory(localDir);
                    }
                    var result = client.DownloadFile(localPath, remotePath, overwrite ? FtpLocalExists.Overwrite : FtpLocalExists.Skip, FtpVerify.None, progress);
                    return result == FtpStatus.Success;
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"下载文件失败: {GetDetailedErrorMessage(ex)}", ex);
            }
        }

        /// <summary>
        /// 异步带进度反馈的下载文件
        /// </summary>
        /// <param name="remotePath">远程文件路径</param>
        /// <param name="localPath">本地文件路径</param>
        /// <param name="progress">进度回调</param>
        /// <param name="overwrite">是否覆盖</param>
        /// <param name="token">取消令牌</param>
        /// <returns>是否成功</returns>
        public async Task<bool> DownloadFileWithProgressAsync(string remotePath, string localPath,
            IProgress<FtpProgress> progress = null, bool overwrite = true, CancellationToken token = default(CancellationToken))
        {
            try
            {
                if (string.IsNullOrWhiteSpace(remotePath))
                    throw new ArgumentException("远程文件路径不能为空", nameof(remotePath));
                if (string.IsNullOrWhiteSpace(localPath))
                    throw new ArgumentException("本地文件路径不能为空", nameof(localPath));

                using (var client = CreateAsyncFtpClient())
                {
                    await EnsureConnectedAsync(client);

                    // 确保本地目录存在
                    var localDir = Path.GetDirectoryName(localPath);
                    if (!string.IsNullOrEmpty(localDir) && !Directory.Exists(localDir))
                    {
                        Directory.CreateDirectory(localDir);
                    }

                    var result = await client.DownloadFile(localPath, remotePath,
                        overwrite ? FtpLocalExists.Overwrite : FtpLocalExists.Skip,
                        FtpVerify.None, progress, token);

                    return result == FtpStatus.Success;
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"下载文件失败: {GetDetailedErrorMessage(ex)}", ex);
            }
        }

        /// <summary>
        /// 断点续传下载文件
        /// </summary>
        /// <param name="remotePath">远程文件路径</param>
        /// <param name="localPath">本地文件路径</param>
        /// <param name="retryAttempts">重试次数</param>
        /// <param name="progress">进度回调</param>
        /// <returns>是否成功</returns>
        public bool DownloadFileWithResume(string remotePath, string localPath, int retryAttempts = 3,
            Action<FtpProgress> progress = null)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(remotePath))
                    throw new ArgumentException("远程文件路径不能为空", nameof(remotePath));
                if (string.IsNullOrWhiteSpace(localPath))
                    throw new ArgumentException("本地文件路径不能为空", nameof(localPath));

                using (var client = CreateFtpClient())
                {
                    EnsureConnected(client);

                    // 确保本地目录存在
                    var localDir = Path.GetDirectoryName(localPath);
                    if (!string.IsNullOrEmpty(localDir) && !Directory.Exists(localDir))
                    {
                        Directory.CreateDirectory(localDir);
                    }

                    var result = client.DownloadFile(localPath, remotePath,
                        FtpLocalExists.Resume, FtpVerify.None, progress);

                    return result == FtpStatus.Success;
                }
            }
            catch (Exception ex)
            {
                if (retryAttempts > 0)
                {
                    // 重试
                    Thread.Sleep(1000); // 等待1秒后重试
                    return DownloadFileWithResume(remotePath, localPath, retryAttempts - 1, progress);
                }
                throw new Exception($"断点续传下载文件失败: {GetDetailedErrorMessage(ex)}", ex);
            }
        }

        /// <summary>
        /// 异步断点续传下载文件
        /// </summary>
        /// <param name="remotePath">远程文件路径</param>
        /// <param name="localPath">本地文件路径</param>
        /// <param name="retryAttempts">重试次数</param>
        /// <param name="progress">进度回调</param>
        /// <param name="token">取消令牌</param>
        /// <returns>是否成功</returns>
        public async Task<bool> DownloadFileWithResumeAsync(string remotePath, string localPath,
            int retryAttempts = 3, IProgress<FtpProgress> progress = null, CancellationToken token = default(CancellationToken))
        {
            try
            {
                if (string.IsNullOrWhiteSpace(remotePath))
                    throw new ArgumentException("远程文件路径不能为空", nameof(remotePath));
                if (string.IsNullOrWhiteSpace(localPath))
                    throw new ArgumentException("本地文件路径不能为空", nameof(localPath));

                using (var client = CreateAsyncFtpClient())
                {
                    await EnsureConnectedAsync(client);

                    // 确保本地目录存在
                    var localDir = Path.GetDirectoryName(localPath);
                    if (!string.IsNullOrEmpty(localDir) && !Directory.Exists(localDir))
                    {
                        Directory.CreateDirectory(localDir);
                    }

                    var result = await client.DownloadFile(localPath, remotePath,
                        FtpLocalExists.Resume, FtpVerify.None, progress, token);

                    return result == FtpStatus.Success;
                }
            }
            catch (Exception ex)
            {
                if (retryAttempts > 0 && !token.IsCancellationRequested)
                {
                    // 重试
                    await Task.Delay(1000, token); // 等待1秒后重试
                    return await DownloadFileWithResumeAsync(remotePath, localPath, retryAttempts - 1, progress, token);
                }
                throw new Exception($"断点续传下载文件失败: {GetDetailedErrorMessage(ex)}", ex);
            }
        }

        /// <summary>
        /// 下载文件到流
        /// </summary>
        /// <param name="remotePath">远程文件路径</param>
        /// <param name="stream">目标流</param>
        /// <param name="progress">进度回调</param>
        /// <returns>是否成功</returns>
        public bool DownloadFileToStream(string remotePath, Stream stream, Action<FtpProgress> progress = null)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(remotePath))
                    throw new ArgumentException("远程文件路径不能为空", nameof(remotePath));
                if (stream == null)
                    throw new ArgumentNullException(nameof(stream));

                using (var client = CreateFtpClient())
                {
                    EnsureConnected(client);
                    return client.DownloadStream(stream, remotePath, 0, progress);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"下载文件到流失败: {GetDetailedErrorMessage(ex)}", ex);
            }
        }

        /// <summary>
        /// 异步下载文件到流
        /// </summary>
        /// <param name="remotePath">远程文件路径</param>
        /// <param name="stream">目标流</param>
        /// <param name="progress">进度回调</param>
        /// <param name="token">取消令牌</param>
        /// <returns>是否成功</returns>
        public async Task<bool> DownloadFileToStreamAsync(string remotePath, Stream stream,
            IProgress<FtpProgress> progress = null, CancellationToken token = default(CancellationToken))
        {
            try
            {
                if (string.IsNullOrWhiteSpace(remotePath))
                    throw new ArgumentException("远程文件路径不能为空", nameof(remotePath));
                if (stream == null)
                    throw new ArgumentNullException(nameof(stream));

                using (var client = CreateAsyncFtpClient())
                {
                    await EnsureConnectedAsync(client);
                    return await client.DownloadStream(stream, remotePath, 0, progress, token);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"下载文件到流失败: {GetDetailedErrorMessage(ex)}", ex);
            }
        }

        #endregion

        #region 流式上传和批量操作

        /// <summary>
        /// 从Web上传控件直接上传到FTP
        /// </summary>
        /// <param name="fileUpload">Web文件上传控件</param>
        /// <param name="remotePath">远程文件路径</param>
        /// <param name="overwrite">是否覆盖</param>
        /// <param name="progress">进度回调</param>
        /// <returns>是否成功</returns>
        public bool UploadFromWebControl(System.Web.UI.WebControls.FileUpload fileUpload, string remotePath,
            bool overwrite = true, Action<FtpProgress> progress = null)
        {
            try
            {
                if (fileUpload == null || !fileUpload.HasFile)
                    throw new ArgumentException("文件上传控件为空或未选择文件", nameof(fileUpload));
                if (string.IsNullOrWhiteSpace(remotePath))
                    throw new ArgumentException("远程文件路径不能为空", nameof(remotePath));

                using (var stream = fileUpload.PostedFile.InputStream)
                {
                    return UploadFileFromStream(stream, remotePath, overwrite, progress);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"从Web控件上传文件失败: {GetDetailedErrorMessage(ex)}", ex);
            }
        }

        /// <summary>
        /// 异步从Web上传控件直接上传到FTP
        /// </summary>
        /// <param name="fileUpload">Web文件上传控件</param>
        /// <param name="remotePath">远程文件路径</param>
        /// <param name="overwrite">是否覆盖</param>
        /// <param name="progress">进度回调</param>
        /// <param name="token">取消令牌</param>
        /// <returns>是否成功</returns>
        public async Task<bool> UploadFromWebControlAsync(System.Web.UI.WebControls.FileUpload fileUpload,
            string remotePath, bool overwrite = true, IProgress<FtpProgress> progress = null,
            CancellationToken token = default(CancellationToken))
        {
            try
            {
                if (fileUpload == null || !fileUpload.HasFile)
                    throw new ArgumentException("文件上传控件为空或未选择文件", nameof(fileUpload));
                if (string.IsNullOrWhiteSpace(remotePath))
                    throw new ArgumentException("远程文件路径不能为空", nameof(remotePath));

                using (var stream = fileUpload.PostedFile.InputStream)
                {
                    return await UploadFileFromStreamAsync(stream, remotePath, overwrite, progress, token);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"从Web控件上传文件失败: {GetDetailedErrorMessage(ex)}", ex);
            }
        }

        /// <summary>
        /// 批量上传文件
        /// </summary>
        /// <param name="localFiles">本地文件路径列表</param>
        /// <param name="remoteDirectory">远程目录</param>
        /// <param name="useResume">是否使用断点续传</param>
        /// <param name="progress">进度回调</param>
        /// <returns>成功上传的文件数量</returns>
        public int BatchUploadFiles(List<string> localFiles, string remoteDirectory, bool useResume = false,
            Action<FtpProgress> progress = null)
        {
            try
            {
                if (localFiles == null || localFiles.Count == 0)
                    throw new ArgumentException("文件列表不能为空", nameof(localFiles));
                if (string.IsNullOrWhiteSpace(remoteDirectory))
                    throw new ArgumentException("远程目录不能为空", nameof(remoteDirectory));

                using (var client = CreateFtpClient())
                {
                    EnsureConnected(client);
                }

                // 确保远程目录存在
                if (!DirectoryExists(remoteDirectory))
                {
                    CreateDirectory(remoteDirectory);
                }

                int successCount = 0;
                foreach (var localFile in localFiles)
                {
                    try
                    {
                        if (!File.Exists(localFile))
                            continue;

                        var fileName = Path.GetFileName(localFile);
                        var remotePath = $"{remoteDirectory.TrimEnd('/')}/{fileName}";

                        bool success;
                        if (useResume)
                        {
                            success = UploadFileWithResume(localFile, remotePath, 3, progress);
                        }
                        else
                        {
                            success = UploadFileWithProgress(localFile, remotePath, progress);
                        }

                        if (success)
                            successCount++;
                    }
                    catch
                    {
                        // 忽略单个文件的错误，继续处理其他文件
                        continue;
                    }
                }

                return successCount;
            }
            catch (Exception ex)
            {
                throw new Exception($"批量上传文件失败: {GetDetailedErrorMessage(ex)}", ex);
            }
        }

        /// <summary>
        /// 异步批量上传文件
        /// </summary>
        /// <param name="localFiles">本地文件路径列表</param>
        /// <param name="remoteDirectory">远程目录</param>
        /// <param name="useResume">是否使用断点续传</param>
        /// <param name="progress">进度回调</param>
        /// <param name="token">取消令牌</param>
        /// <returns>成功上传的文件数量</returns>
        public async Task<int> BatchUploadFilesAsync(List<string> localFiles, string remoteDirectory,
            bool useResume = false, IProgress<FtpProgress> progress = null, CancellationToken token = default(CancellationToken))
        {
            try
            {
                if (localFiles == null || localFiles.Count == 0)
                    throw new ArgumentException("文件列表不能为空", nameof(localFiles));
                if (string.IsNullOrWhiteSpace(remoteDirectory))
                    throw new ArgumentException("远程目录不能为空", nameof(remoteDirectory));

                using (var client = CreateAsyncFtpClient())

                    // 确保远程目录存在
                    if (!await DirectoryExistsAsync(remoteDirectory))
                    {
                        await CreateDirectoryAsync(remoteDirectory);
                    }

                int successCount = 0;
                foreach (var localFile in localFiles)
                {
                    try
                    {
                        if (token.IsCancellationRequested)
                            break;

                        if (!File.Exists(localFile))
                            continue;

                        var fileName = Path.GetFileName(localFile);
                        var remotePath = $"{remoteDirectory.TrimEnd('/')}/{fileName}";

                        bool success;
                        if (useResume)
                        {
                            success = await UploadFileWithResumeAsync(localFile, remotePath, 3, progress, token);
                        }
                        else
                        {
                            success = await UploadFileWithProgressAsync(localFile, remotePath, progress, true, token);
                        }

                        if (success)
                            successCount++;
                    }
                    catch
                    {
                        // 忽略单个文件的错误，继续处理其他文件
                        continue;
                    }
                }

                return successCount;
            }
            catch (Exception ex)
            {
                throw new Exception($"批量上传文件失败: {GetDetailedErrorMessage(ex)}", ex);
            }
        }

        /// <summary>
        /// 批量下载文件
        /// </summary>
        /// <param name="remoteFiles">远程文件路径列表</param>
        /// <param name="localDirectory">本地目录</param>
        /// <param name="useResume">是否使用断点续传</param>
        /// <param name="progress">进度回调</param>
        /// <returns>成功下载的文件数量</returns>
        public int BatchDownloadFiles(List<string> remoteFiles, string localDirectory, bool useResume = false,
            Action<FtpProgress> progress = null)
        {
            try
            {
                if (remoteFiles == null || remoteFiles.Count == 0)
                    throw new ArgumentException("文件列表不能为空", nameof(remoteFiles));
                if (string.IsNullOrWhiteSpace(localDirectory))
                    throw new ArgumentException("本地目录不能为空", nameof(localDirectory));

                using (var client = CreateFtpClient())
                {
                    EnsureConnected(client);
                }

                // 确保本地目录存在
                if (!Directory.Exists(localDirectory))
                {
                    Directory.CreateDirectory(localDirectory);
                }

                int successCount = 0;
                foreach (var remoteFile in remoteFiles)
                {
                    try
                    {
                        if (!FileExists(remoteFile))
                            continue;

                        var fileName = Path.GetFileName(remoteFile);
                        var localPath = Path.Combine(localDirectory, fileName);

                        bool success;
                        if (useResume)
                        {
                            success = DownloadFileWithResume(remoteFile, localPath, 3, progress);
                        }
                        else
                        {
                            success = DownloadFileWithProgress(remoteFile, localPath, progress);
                        }

                        if (success)
                            successCount++;
                    }
                    catch
                    {
                        // 忽略单个文件的错误，继续处理其他文件
                        continue;
                    }
                }

                return successCount;
            }
            catch (Exception ex)
            {
                throw new Exception($"批量下载文件失败: {GetDetailedErrorMessage(ex)}", ex);
            }
        }

        /// <summary>
        /// 异步批量下载文件
        /// </summary>
        /// <param name="remoteFiles">远程文件路径列表</param>
        /// <param name="localDirectory">本地目录</param>
        /// <param name="useResume">是否使用断点续传</param>
        /// <param name="progress">进度回调</param>
        /// <param name="token">取消令牌</param>
        /// <returns>成功下载的文件数量</returns>
        public async Task<int> BatchDownloadFilesAsync(List<string> remoteFiles, string localDirectory,
            bool useResume = false, IProgress<FtpProgress> progress = null, CancellationToken token = default(CancellationToken))
        {
            try
            {
                if (remoteFiles == null || remoteFiles.Count == 0)
                    throw new ArgumentException("文件列表不能为空", nameof(remoteFiles));
                if (string.IsNullOrWhiteSpace(localDirectory))
                    throw new ArgumentException("本地目录不能为空", nameof(localDirectory));

                using (var client = CreateAsyncFtpClient())

                    // 确保本地目录存在
                    if (!Directory.Exists(localDirectory))
                    {
                        Directory.CreateDirectory(localDirectory);
                    }

                int successCount = 0;
                foreach (var remoteFile in remoteFiles)
                {
                    try
                    {
                        if (token.IsCancellationRequested)
                            break;

                        if (!await FileExistsAsync(remoteFile))
                            continue;

                        var fileName = Path.GetFileName(remoteFile);
                        var localPath = Path.Combine(localDirectory, fileName);

                        bool success;
                        if (useResume)
                        {
                            success = await DownloadFileWithResumeAsync(remoteFile, localPath, 3, progress, token);
                        }
                        else
                        {
                            success = await DownloadFileWithProgressAsync(remoteFile, localPath, progress, true, token);
                        }

                        if (success)
                            successCount++;
                    }
                    catch
                    {
                        // 忽略单个文件的错误，继续处理其他文件
                        continue;
                    }
                }

                return successCount;
            }
            catch (Exception ex)
            {
                throw new Exception($"批量下载文件失败: {GetDetailedErrorMessage(ex)}", ex);
            }
        }

        /// <summary>
        /// 同步整个目录
        /// </summary>
        /// <param name="localDirectory">本地目录</param>
        /// <param name="remoteDirectory">远程目录</param>
        /// <param name="mode">同步模式</param>
        /// <param name="progress">进度回调</param>
        /// <returns>同步结果</returns>
        public List<FtpResult> SynchronizeDirectory(string localDirectory, string remoteDirectory,
            FtpFolderSyncMode mode = FtpFolderSyncMode.Update, Action<FtpProgress> progress = null)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(localDirectory))
                    throw new ArgumentException("本地目录不能为空", nameof(localDirectory));
                if (string.IsNullOrWhiteSpace(remoteDirectory))
                    throw new ArgumentException("远程目录不能为空", nameof(remoteDirectory));

                using (var client = CreateFtpClient())
                {
                    EnsureConnected(client);
                    return client.UploadDirectory(localDirectory, remoteDirectory, mode,
                    FtpRemoteExists.Overwrite, FtpVerify.None, null, progress);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"同步目录失败: {GetDetailedErrorMessage(ex)}", ex);
            }
        }

        /// <summary>
        /// 异步同步整个目录
        /// </summary>
        /// <param name="localDirectory">本地目录</param>
        /// <param name="remoteDirectory">远程目录</param>
        /// <param name="mode">同步模式</param>
        /// <param name="progress">进度回调</param>
        /// <param name="token">取消令牌</param>
        /// <returns>同步结果</returns>
        public async Task<List<FtpResult>> SynchronizeDirectoryAsync(string localDirectory, string remoteDirectory,
            FtpFolderSyncMode mode = FtpFolderSyncMode.Update, IProgress<FtpProgress> progress = null,
            CancellationToken token = default(CancellationToken))
        {
            try
            {
                if (string.IsNullOrWhiteSpace(localDirectory))
                    throw new ArgumentException("本地目录不能为空", nameof(localDirectory));
                if (string.IsNullOrWhiteSpace(remoteDirectory))
                    throw new ArgumentException("远程目录不能为空", nameof(remoteDirectory));

                using (var client = CreateAsyncFtpClient())
                {
                    await EnsureConnectedAsync(client);
                    return await client.UploadDirectory(localDirectory, remoteDirectory, mode,
                       FtpRemoteExists.Overwrite, FtpVerify.None, null, progress, token);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"同步目录失败: {GetDetailedErrorMessage(ex)}", ex);
            }
        }

        #endregion

        #region IDisposable实现

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        /// <param name="disposing">是否正在释放</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                _disposed = true;
            }
        }

        /// <summary>
        /// 析构函数
        /// </summary>
        ~FtpHelperFluentFTP()
        {
            Dispose(false);
        }

        #endregion
    }
}
