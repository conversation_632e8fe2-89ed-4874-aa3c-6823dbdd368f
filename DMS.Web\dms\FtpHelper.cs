﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using System.Web;
// 注意：需要安装 FluentFTP NuGet 包
// Install-Package FluentFTP -Version 35.0.5 (适用于.NET Framework 4.6)
// using FluentFTP;

namespace DMS.Web.dms
{
    /// <summary>
    /// FTP项目信息类
    /// </summary>
    public class FtpItemInfo
    {
        /// <summary>
        /// 项目名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 是否为目录
        /// </summary>
        public bool IsDirectory { get; set; }

        /// <summary>
        /// 文件大小（字节）
        /// </summary>
        public long Size { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime ModifyTime { get; set; }

        /// <summary>
        /// 项目类型描述
        /// </summary>
        public string TypeDescription
        {
            get { return IsDirectory ? "目录" : "文件"; }
        }

        /// <summary>
        /// 格式化的文件大小
        /// </summary>
        public string FormattedSize
        {
            get
            {
                if (IsDirectory) return "-";
                if (Size < 1024) return $"{Size} B";
                if (Size < 1024 * 1024) return $"{Size / 1024.0:F2} KB";
                if (Size < 1024 * 1024 * 1024) return $"{Size / (1024.0 * 1024):F2} MB";
                return $"{Size / (1024.0 * 1024 * 1024):F2} GB";
            }
        }
    }

    /// <summary>
    /// FTP操作工具类
    /// </summary>
    public class FtpHelper
    {
        #region 私有字段
        private string _server;
        private string _username;
        private string _password;
        private int _port;
        private int _timeout;
        #endregion

        #region 构造函数
        public FtpHelper()
        {
            _port = 21;
            _timeout = 30000; // 30秒超时
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="server">服务器地址</param>
        /// <param name="username">用户名</param>
        /// <param name="password">密码</param>
        /// <param name="port">端口号，默认21</param>
        /// <param name="timeout">超时时间（毫秒），默认30000</param>
        public FtpHelper(string server, string username, string password, int port = 21, int timeout = 30000)
        {
            _server = server;
            _username = username;
            _password = password;
            _port = port;
            _timeout = timeout;
        }
        #endregion

        #region 属性
        /// <summary>
        /// FTP服务器地址
        /// </summary>
        public string Server
        {
            get { return _server; }
            set { _server = value; }
        }

        /// <summary>
        /// 用户名
        /// </summary>
        public string Username
        {
            get { return _username; }
            set { _username = value; }
        }

        /// <summary>
        /// 密码
        /// </summary>
        public string Password
        {
            get { return _password; }
            set { _password = value; }
        }

        /// <summary>
        /// 端口号
        /// </summary>
        public int Port
        {
            get { return _port; }
            set { _port = value; }
        }

        /// <summary>
        /// 超时时间（毫秒）
        /// </summary>
        public int Timeout
        {
            get { return _timeout; }
            set { _timeout = value; }
        }
        #endregion

        #region 委托和事件
        /// <summary>
        /// 进度回调委托
        /// </summary>
        /// <param name="bytesTransferred">已传输字节数</param>
        /// <param name="totalBytes">总字节数</param>
        public delegate void ProgressCallback(long bytesTransferred, long totalBytes);

        /// <summary>
        /// 上传进度事件
        /// </summary>
        public event ProgressCallback UploadProgress;

        /// <summary>
        /// 下载进度事件
        /// </summary>
        public event ProgressCallback DownloadProgress;
        #endregion

        #region 私有方法
        /// <summary>
        /// 创建FTP请求
        /// </summary>
        /// <param name="uri">FTP地址</param>
        /// <param name="method">FTP方法</param>
        /// <returns>FTP请求对象</returns>
        private FtpWebRequest CreateFtpWebRequest(string uri, string method)
        {
            FtpWebRequest request = (FtpWebRequest)WebRequest.Create(uri);
            request.Method = method;
            request.Credentials = new NetworkCredential(_username, _password);
            request.Timeout = _timeout;
            request.KeepAlive = false;
            request.UseBinary = true;
            request.UsePassive = true;
            return request;
        }

        /// <summary>
        /// 获取FTP完整地址
        /// </summary>
        /// <param name="remotePath">远程路径</param>
        /// <returns>完整FTP地址</returns>
        private string GetFtpUri(string remotePath)
        {
            if (string.IsNullOrEmpty(remotePath))
                remotePath = "/";

            if (!remotePath.StartsWith("/"))
                remotePath = "/" + remotePath;

            return $"ftp://{_server}:{_port}{remotePath}";
        }

        /// <summary>
        /// 执行FTP命令并获取响应
        /// </summary>
        /// <param name="uri">FTP地址</param>
        /// <param name="method">FTP方法</param>
        /// <returns>响应字符串</returns>
        private string ExecuteFtpCommand(string uri, string method)
        {
            FtpWebRequest request = CreateFtpWebRequest(uri, method);
            using (FtpWebResponse response = (FtpWebResponse)request.GetResponse())
            {
                using (StreamReader reader = new StreamReader(response.GetResponseStream(), Encoding.UTF8))
                {
                    return reader.ReadToEnd();
                }
            }
        }
        #endregion

        #region 连接测试
        /// <summary>
        /// 测试FTP连接
        /// </summary>
        /// <returns>连接是否成功</returns>
        public bool TestConnection()
        {
            try
            {
                string uri = GetFtpUri("/");
                FtpWebRequest request = CreateFtpWebRequest(uri, "MLSD");
                using (FtpWebResponse response = (FtpWebResponse)request.GetResponse())
                {
                    return response.StatusCode == FtpStatusCode.OpeningData ||
                           response.StatusCode == FtpStatusCode.DataAlreadyOpen;
                }
            }
            catch
            {
                return false;
            }
        }
        #endregion

        #region 目录操作
        /// <summary>
        /// 创建目录
        /// </summary>
        /// <param name="remotePath">远程目录路径</param>
        /// <returns>是否创建成功</returns>
        public bool CreateDirectory(string remotePath)
        {
            try
            {
                if (DirectoryExists(remotePath))
                    return true;

                string uri = GetFtpUri(remotePath);
                FtpWebRequest request = CreateFtpWebRequest(uri, WebRequestMethods.Ftp.MakeDirectory);
                using (FtpWebResponse response = (FtpWebResponse)request.GetResponse())
                {
                    return response.StatusCode == FtpStatusCode.PathnameCreated;
                }
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 删除目录
        /// </summary>
        /// <param name="remotePath">远程目录路径</param>
        /// <returns>是否删除成功</returns>
        public bool DeleteDirectory(string remotePath)
        {
            try
            {
                if (!DirectoryExists(remotePath))
                    return true;

                string uri = GetFtpUri(remotePath);
                FtpWebRequest request = CreateFtpWebRequest(uri, WebRequestMethods.Ftp.RemoveDirectory);
                using (FtpWebResponse response = (FtpWebResponse)request.GetResponse())
                {
                    return response.StatusCode == FtpStatusCode.FileActionOK;
                }
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 重命名目录
        /// </summary>
        /// <param name="oldPath">原目录路径</param>
        /// <param name="newPath">新目录路径</param>
        /// <returns>是否重命名成功</returns>
        public bool RenameDirectory(string oldPath, string newPath)
        {
            try
            {
                if (!DirectoryExists(oldPath))
                    return false;

                string uri = GetFtpUri(oldPath);
                FtpWebRequest request = CreateFtpWebRequest(uri, WebRequestMethods.Ftp.Rename);
                request.RenameTo = newPath;
                using (FtpWebResponse response = (FtpWebResponse)request.GetResponse())
                {
                    return response.StatusCode == FtpStatusCode.FileActionOK;
                }
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 检查目录是否存在
        /// </summary>
        /// <param name="remotePath">远程目录路径</param>
        /// <returns>目录是否存在</returns>
        public bool DirectoryExists(string remotePath)
        {
            try
            {
                string uri = GetFtpUri(remotePath);
                FtpWebRequest request = CreateFtpWebRequest(uri, "MLSD");
                using (FtpWebResponse response = (FtpWebResponse)request.GetResponse())
                {
                    return true;
                }
            }
            catch (WebException ex)
            {
                FtpWebResponse response = (FtpWebResponse)ex.Response;
                return response?.StatusCode != FtpStatusCode.ActionNotTakenFileUnavailable;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 获取目录列表
        /// </summary>
        /// <param name="remotePath">远程目录路径</param>
        /// <returns>目录列表</returns>
        public List<string> GetDirectoryList(string remotePath = "/")
        {
            List<string> directories = new List<string>();
            try
            {
                string uri = GetFtpUri(remotePath);
                string result = ExecuteFtpCommand(uri, "MLSD");
                string[] lines = result.Split(new char[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);

                foreach (string line in lines)
                {
                    if (!string.IsNullOrEmpty(line))
                    {
                        // MLSD格式: type=dir;size=4096;modify=20231201120000; dirname
                        // 或者: type=file;size=1024;modify=20231201120000; filename
                        string[] parts = line.Split(new char[] { ' ' }, 2);
                        if (parts.Length >= 2)
                        {
                            string facts = parts[0];
                            string name = parts[1];

                            // 检查是否为目录
                            if (facts.Contains("type=dir") || facts.Contains("type=cdir") || facts.Contains("type=pdir"))
                            {
                                // 排除当前目录(.)和父目录(..)
                                if (name != "." && name != "..")
                                {
                                    directories.Add(name);
                                }
                            }
                        }
                    }
                }
            }
            catch
            {
                // 返回空列表
            }
            return directories;
        }

        /// <summary>
        /// 获取目录和文件的详细信息列表
        /// </summary>
        /// <param name="remotePath">远程目录路径</param>
        /// <returns>包含类型、名称、大小等信息的列表</returns>
        public List<FtpItemInfo> GetDetailedDirectoryList(string remotePath = "/")
        {
            List<FtpItemInfo> items = new List<FtpItemInfo>();
            try
            {
                string uri = GetFtpUri(remotePath);
                string result = ExecuteFtpCommand(uri, "MLSD");
                string[] lines = result.Split(new char[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);

                foreach (string line in lines)
                {
                    if (!string.IsNullOrEmpty(line))
                    {
                        FtpItemInfo item = ParseMLSDLine(line);
                        if (item != null && item.Name != "." && item.Name != "..")
                        {
                            items.Add(item);
                        }
                    }
                }
            }
            catch
            {
                // 如果MLSD失败，尝试使用传统的LIST命令
                try
                {
                    string uri = GetFtpUri(remotePath);
                    string result = ExecuteFtpCommand(uri, WebRequestMethods.Ftp.ListDirectoryDetails);
                    string[] lines = result.Split(new char[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);

                    foreach (string line in lines)
                    {
                        if (!string.IsNullOrEmpty(line))
                        {
                            FtpItemInfo item = ParseLISTLine(line);
                            if (item != null && item.Name != "." && item.Name != "..")
                            {
                                items.Add(item);
                            }
                        }
                    }
                }
                catch
                {
                    // 返回空列表
                }
            }
            return items;
        }

        /// <summary>
        /// 解析MLSD命令返回的行
        /// </summary>
        /// <param name="line">MLSD行</param>
        /// <returns>FTP项目信息</returns>
        private FtpItemInfo ParseMLSDLine(string line)
        {
            try
            {
                string[] parts = line.Split(new char[] { ' ' }, 2);
                if (parts.Length >= 2)
                {
                    string facts = parts[0];
                    string name = parts[1];

                    FtpItemInfo item = new FtpItemInfo { Name = name };

                    // 解析facts
                    string[] factPairs = facts.Split(';');
                    foreach (string factPair in factPairs)
                    {
                        if (factPair.Contains("="))
                        {
                            string[] kv = factPair.Split('=');
                            if (kv.Length == 2)
                            {
                                switch (kv[0].ToLower())
                                {
                                    case "type":
                                        item.IsDirectory = kv[1] == "dir" || kv[1] == "cdir" || kv[1] == "pdir";
                                        break;
                                    case "size":
                                        long size;
                                        item.Size = long.TryParse(kv[1], out size) ? size : 0;
                                        break;
                                    case "modify":
                                        item.ModifyTime = ParseMLSDTime(kv[1]);
                                        break;
                                }
                            }
                        }
                    }

                    return item;
                }
            }
            catch
            {
                // 解析失败
            }
            return null;
        }

        /// <summary>
        /// 解析传统LIST命令返回的行
        /// </summary>
        /// <param name="line">LIST行</param>
        /// <returns>FTP项目信息</returns>
        private FtpItemInfo ParseLISTLine(string line)
        {
            try
            {
                // 简单的Unix风格LIST解析
                // 格式: drwxr-xr-x 2 <USER> <GROUP> 4096 Dec 01 12:00 dirname
                string[] parts = line.Split(new char[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);
                if (parts.Length >= 9)
                {
                    FtpItemInfo item = new FtpItemInfo();
                    item.IsDirectory = line.StartsWith("d");
                    item.Name = string.Join(" ", parts, 8, parts.Length - 8);

                    if (!item.IsDirectory && parts.Length >= 5)
                    {
                        long size;
                        if (long.TryParse(parts[4], out size))
                        {
                            item.Size = size;
                        }
                    }

                    return item;
                }
            }
            catch
            {
                // 解析失败
            }
            return null;
        }

        /// <summary>
        /// 解析MLSD时间格式
        /// </summary>
        /// <param name="timeStr">时间字符串</param>
        /// <returns>DateTime对象</returns>
        private DateTime ParseMLSDTime(string timeStr)
        {
            try
            {
                // MLSD时间格式: YYYYMMDDHHMMSS
                if (timeStr.Length >= 14)
                {
                    int year = int.Parse(timeStr.Substring(0, 4));
                    int month = int.Parse(timeStr.Substring(4, 2));
                    int day = int.Parse(timeStr.Substring(6, 2));
                    int hour = int.Parse(timeStr.Substring(8, 2));
                    int minute = int.Parse(timeStr.Substring(10, 2));
                    int second = int.Parse(timeStr.Substring(12, 2));

                    return new DateTime(year, month, day, hour, minute, second);
                }
            }
            catch
            {
                // 解析失败
            }
            return DateTime.MinValue;
        }
        #endregion

        #region 文件操作
        /// <summary>
        /// 删除文件
        /// </summary>
        /// <param name="remoteFilePath">远程文件路径</param>
        /// <returns>是否删除成功</returns>
        public bool DeleteFile(string remoteFilePath)
        {
            try
            {
                if (!FileExists(remoteFilePath))
                    return true;

                string uri = GetFtpUri(remoteFilePath);
                FtpWebRequest request = CreateFtpWebRequest(uri, WebRequestMethods.Ftp.DeleteFile);
                using (FtpWebResponse response = (FtpWebResponse)request.GetResponse())
                {
                    return response.StatusCode == FtpStatusCode.FileActionOK;
                }
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 重命名文件
        /// </summary>
        /// <param name="oldFilePath">原文件路径</param>
        /// <param name="newFilePath">新文件路径</param>
        /// <returns>是否重命名成功</returns>
        public bool RenameFile(string oldFilePath, string newFilePath)
        {
            try
            {
                if (!FileExists(oldFilePath))
                    return false;

                string uri = GetFtpUri(oldFilePath);
                FtpWebRequest request = CreateFtpWebRequest(uri, WebRequestMethods.Ftp.Rename);
                request.RenameTo = newFilePath;
                using (FtpWebResponse response = (FtpWebResponse)request.GetResponse())
                {
                    return response.StatusCode == FtpStatusCode.FileActionOK;
                }
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 检查文件是否存在
        /// </summary>
        /// <param name="remoteFilePath">远程文件路径</param>
        /// <returns>文件是否存在</returns>
        public bool FileExists(string remoteFilePath)
        {
            try
            {
                string uri = GetFtpUri(remoteFilePath);
                FtpWebRequest request = CreateFtpWebRequest(uri, WebRequestMethods.Ftp.GetFileSize);
                using (FtpWebResponse response = (FtpWebResponse)request.GetResponse())
                {
                    return true;
                }
            }
            catch (WebException ex)
            {
                FtpWebResponse response = (FtpWebResponse)ex.Response;
                return response?.StatusCode != FtpStatusCode.ActionNotTakenFileUnavailable;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 获取文件大小
        /// </summary>
        /// <param name="remoteFilePath">远程文件路径</param>
        /// <returns>文件大小（字节），-1表示获取失败</returns>
        public long GetFileSize(string remoteFilePath)
        {
            try
            {
                string uri = GetFtpUri(remoteFilePath);
                FtpWebRequest request = CreateFtpWebRequest(uri, WebRequestMethods.Ftp.GetFileSize);
                using (FtpWebResponse response = (FtpWebResponse)request.GetResponse())
                {
                    return response.ContentLength;
                }
            }
            catch
            {
                return -1;
            }
        }

        /// <summary>
        /// 获取文件列表
        /// </summary>
        /// <param name="remotePath">远程目录路径</param>
        /// <returns>文件列表</returns>
        public List<string> GetFileList(string remotePath = "/")
        {
            List<string> files = new List<string>();
            try
            {
                string uri = GetFtpUri(remotePath);
                string result = ExecuteFtpCommand(uri, "MLSD");
                string[] lines = result.Split(new char[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);

                foreach (string line in lines)
                {
                    if (!string.IsNullOrEmpty(line))
                    {
                        // MLSD格式: type=file;size=1024;modify=20231201120000; filename
                        string[] parts = line.Split(new char[] { ' ' }, 2);
                        if (parts.Length >= 2)
                        {
                            string facts = parts[0];
                            string name = parts[1];

                            // 检查是否为文件
                            if (facts.Contains("type=file") || (!facts.Contains("type=dir") && !facts.Contains("type=cdir") && !facts.Contains("type=pdir")))
                            {
                                files.Add(name);
                            }
                        }
                    }
                }
            }
            catch
            {
                // 返回空列表
            }
            return files;
        }
        #endregion

        #region 文件上传下载
        /// <summary>
        /// 上传文件
        /// </summary>
        /// <param name="localFilePath">本地文件路径</param>
        /// <param name="remoteFilePath">远程文件路径</param>
        /// <returns>是否上传成功</returns>
        public bool UploadFile(string localFilePath, string remoteFilePath)
        {
            try
            {
                if (!File.Exists(localFilePath))
                    return false;

                string uri = GetFtpUri(remoteFilePath);
                FtpWebRequest request = CreateFtpWebRequest(uri, WebRequestMethods.Ftp.UploadFile);

                using (FileStream fileStream = new FileStream(localFilePath, FileMode.Open, FileAccess.Read))
                using (Stream requestStream = request.GetRequestStream())
                {
                    byte[] buffer = new byte[8192];
                    int bytesRead;
                    long totalBytes = fileStream.Length;
                    long bytesTransferred = 0;

                    while ((bytesRead = fileStream.Read(buffer, 0, buffer.Length)) > 0)
                    {
                        requestStream.Write(buffer, 0, bytesRead);
                        bytesTransferred += bytesRead;

                        // 触发进度事件
                        UploadProgress?.Invoke(bytesTransferred, totalBytes);
                    }
                }

                using (FtpWebResponse response = (FtpWebResponse)request.GetResponse())
                {
                    return response.StatusCode == FtpStatusCode.ClosingData;
                }
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 下载文件
        /// </summary>
        /// <param name="remoteFilePath">远程文件路径</param>
        /// <param name="localFilePath">本地文件路径</param>
        /// <returns>是否下载成功</returns>
        public bool DownloadFile(string remoteFilePath, string localFilePath)
        {
            try
            {
                if (!FileExists(remoteFilePath))
                    return false;

                // 确保本地目录存在
                string localDir = Path.GetDirectoryName(localFilePath);
                if (!Directory.Exists(localDir))
                    Directory.CreateDirectory(localDir);

                string uri = GetFtpUri(remoteFilePath);
                FtpWebRequest request = CreateFtpWebRequest(uri, WebRequestMethods.Ftp.DownloadFile);

                using (FtpWebResponse response = (FtpWebResponse)request.GetResponse())
                using (Stream responseStream = response.GetResponseStream())
                using (FileStream fileStream = new FileStream(localFilePath, FileMode.Create, FileAccess.Write))
                {
                    byte[] buffer = new byte[8192];
                    int bytesRead;
                    long totalBytes = response.ContentLength;
                    long bytesTransferred = 0;

                    while ((bytesRead = responseStream.Read(buffer, 0, buffer.Length)) > 0)
                    {
                        fileStream.Write(buffer, 0, bytesRead);
                        bytesTransferred += bytesRead;

                        // 触发进度事件
                        DownloadProgress?.Invoke(bytesTransferred, totalBytes);
                    }
                }

                return true;
            }
            catch
            {
                return false;
            }
        }
        #endregion

        #region 断点续传
        /// <summary>
        /// 断点续传上传文件
        /// </summary>
        /// <param name="localFilePath">本地文件路径</param>
        /// <param name="remoteFilePath">远程文件路径</param>
        /// <param name="maxRetries">最大重试次数</param>
        /// <returns>是否上传成功</returns>
        public bool UploadFileWithResume(string localFilePath, string remoteFilePath, int maxRetries = 3)
        {
            try
            {
                if (!File.Exists(localFilePath))
                    return false;

                FileInfo localFileInfo = new FileInfo(localFilePath);
                long localFileSize = localFileInfo.Length;
                long remoteFileSize = 0;

                // 检查远程文件是否存在，如果存在则获取大小
                if (FileExists(remoteFilePath))
                {
                    remoteFileSize = GetFileSize(remoteFilePath);
                    if (remoteFileSize == localFileSize)
                    {
                        // 文件已完全上传
                        return true;
                    }
                    if (remoteFileSize > localFileSize)
                    {
                        // 远程文件比本地文件大，删除远程文件重新上传
                        DeleteFile(remoteFilePath);
                        remoteFileSize = 0;
                    }
                }

                int retryCount = 0;
                while (retryCount < maxRetries)
                {
                    try
                    {
                        if (UploadFileFromPosition(localFilePath, remoteFilePath, remoteFileSize))
                        {
                            return true;
                        }
                    }
                    catch
                    {
                        // 上传失败，重新获取远程文件大小
                        remoteFileSize = FileExists(remoteFilePath) ? GetFileSize(remoteFilePath) : 0;
                    }

                    retryCount++;
                    if (retryCount < maxRetries)
                    {
                        System.Threading.Thread.Sleep(1000); // 等待1秒后重试
                    }
                }

                return false;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 从指定位置开始上传文件
        /// </summary>
        /// <param name="localFilePath">本地文件路径</param>
        /// <param name="remoteFilePath">远程文件路径</param>
        /// <param name="startPosition">开始位置</param>
        /// <returns>是否上传成功</returns>
        private bool UploadFileFromPosition(string localFilePath, string remoteFilePath, long startPosition)
        {
            string uri = GetFtpUri(remoteFilePath);
            FtpWebRequest request = CreateFtpWebRequest(uri, WebRequestMethods.Ftp.UploadFile);

            // 设置断点续传
            if (startPosition > 0)
            {
                request.ContentOffset = startPosition;
            }

            using (FileStream fileStream = new FileStream(localFilePath, FileMode.Open, FileAccess.Read))
            {
                fileStream.Seek(startPosition, SeekOrigin.Begin);

                using (Stream requestStream = request.GetRequestStream())
                {
                    byte[] buffer = new byte[8192];
                    int bytesRead;
                    long totalBytes = fileStream.Length;
                    long bytesTransferred = startPosition;

                    while ((bytesRead = fileStream.Read(buffer, 0, buffer.Length)) > 0)
                    {
                        requestStream.Write(buffer, 0, bytesRead);
                        bytesTransferred += bytesRead;

                        // 触发进度事件
                        UploadProgress?.Invoke(bytesTransferred, totalBytes);
                    }
                }
            }

            using (FtpWebResponse response = (FtpWebResponse)request.GetResponse())
            {
                return response.StatusCode == FtpStatusCode.ClosingData;
            }
        }

        /// <summary>
        /// 断点续传下载文件
        /// </summary>
        /// <param name="remoteFilePath">远程文件路径</param>
        /// <param name="localFilePath">本地文件路径</param>
        /// <param name="maxRetries">最大重试次数</param>
        /// <returns>是否下载成功</returns>
        public bool DownloadFileWithResume(string remoteFilePath, string localFilePath, int maxRetries = 3)
        {
            try
            {
                if (!FileExists(remoteFilePath))
                    return false;

                // 确保本地目录存在
                string localDir = Path.GetDirectoryName(localFilePath);
                if (!Directory.Exists(localDir))
                    Directory.CreateDirectory(localDir);

                long remoteFileSize = GetFileSize(remoteFilePath);
                if (remoteFileSize <= 0)
                    return false;

                long localFileSize = 0;
                if (File.Exists(localFilePath))
                {
                    FileInfo localFileInfo = new FileInfo(localFilePath);
                    localFileSize = localFileInfo.Length;

                    if (localFileSize == remoteFileSize)
                    {
                        // 文件已完全下载
                        return true;
                    }
                    if (localFileSize > remoteFileSize)
                    {
                        // 本地文件比远程文件大，删除本地文件重新下载
                        File.Delete(localFilePath);
                        localFileSize = 0;
                    }
                }

                int retryCount = 0;
                while (retryCount < maxRetries)
                {
                    try
                    {
                        if (DownloadFileFromPosition(remoteFilePath, localFilePath, localFileSize))
                        {
                            return true;
                        }
                    }
                    catch
                    {
                        // 下载失败，重新获取本地文件大小
                        if (File.Exists(localFilePath))
                        {
                            FileInfo localFileInfo = new FileInfo(localFilePath);
                            localFileSize = localFileInfo.Length;
                        }
                        else
                        {
                            localFileSize = 0;
                        }
                    }

                    retryCount++;
                    if (retryCount < maxRetries)
                    {
                        System.Threading.Thread.Sleep(1000); // 等待1秒后重试
                    }
                }

                return false;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 从指定位置开始下载文件
        /// </summary>
        /// <param name="remoteFilePath">远程文件路径</param>
        /// <param name="localFilePath">本地文件路径</param>
        /// <param name="startPosition">开始位置</param>
        /// <returns>是否下载成功</returns>
        private bool DownloadFileFromPosition(string remoteFilePath, string localFilePath, long startPosition)
        {
            string uri = GetFtpUri(remoteFilePath);
            FtpWebRequest request = CreateFtpWebRequest(uri, WebRequestMethods.Ftp.DownloadFile);

            // 设置断点续传
            if (startPosition > 0)
            {
                request.ContentOffset = startPosition;
            }

            using (FtpWebResponse response = (FtpWebResponse)request.GetResponse())
            using (Stream responseStream = response.GetResponseStream())
            {
                FileMode fileMode = startPosition > 0 ? FileMode.Append : FileMode.Create;
                using (FileStream fileStream = new FileStream(localFilePath, fileMode, FileAccess.Write))
                {
                    byte[] buffer = new byte[8192];
                    int bytesRead;
                    long totalBytes = startPosition + response.ContentLength;
                    long bytesTransferred = startPosition;

                    while ((bytesRead = responseStream.Read(buffer, 0, buffer.Length)) > 0)
                    {
                        fileStream.Write(buffer, 0, bytesRead);
                        bytesTransferred += bytesRead;

                        // 触发进度事件
                        DownloadProgress?.Invoke(bytesTransferred, totalBytes);
                    }
                }
            }

            return true;
        }
        #endregion

        #region 辅助方法
        /// <summary>
        /// 获取FTP服务器系统类型
        /// </summary>
        /// <returns>系统类型</returns>
        public string GetSystemType()
        {
            try
            {
                string uri = GetFtpUri("/");
                FtpWebRequest request = CreateFtpWebRequest(uri, "SYST");
                using (FtpWebResponse response = (FtpWebResponse)request.GetResponse())
                using (StreamReader reader = new StreamReader(response.GetResponseStream(), Encoding.UTF8))
                {
                    return reader.ReadToEnd().Trim();
                }
            }
            catch
            {
                return "Unknown";
            }
        }

        /// <summary>
        /// 获取当前工作目录
        /// </summary>
        /// <returns>当前工作目录</returns>
        public string GetCurrentDirectory()
        {
            try
            {
                string uri = GetFtpUri("/");
                FtpWebRequest request = CreateFtpWebRequest(uri, "PWD");
                using (FtpWebResponse response = (FtpWebResponse)request.GetResponse())
                using (StreamReader reader = new StreamReader(response.GetResponseStream(), Encoding.UTF8))
                {
                    string result = reader.ReadToEnd().Trim();
                    // 解析PWD命令的响应，通常格式为：257 "/path" is current directory
                    if (result.Contains("\""))
                    {
                        int startIndex = result.IndexOf("\"") + 1;
                        int endIndex = result.IndexOf("\"", startIndex);
                        if (endIndex > startIndex)
                        {
                            return result.Substring(startIndex, endIndex - startIndex);
                        }
                    }
                    return "/";
                }
            }
            catch
            {
                return "/";
            }
        }

        /// <summary>
        /// 批量上传文件
        /// </summary>
        /// <param name="localFiles">本地文件路径列表</param>
        /// <param name="remoteDirectory">远程目录</param>
        /// <param name="useResume">是否使用断点续传</param>
        /// <returns>上传成功的文件数量</returns>
        public int BatchUploadFiles(List<string> localFiles, string remoteDirectory, bool useResume = false)
        {
            int successCount = 0;

            foreach (string localFile in localFiles)
            {
                if (File.Exists(localFile))
                {
                    string fileName = Path.GetFileName(localFile);
                    string remoteFilePath = remoteDirectory.TrimEnd('/') + "/" + fileName;

                    bool success = useResume ?
                        UploadFileWithResume(localFile, remoteFilePath) :
                        UploadFile(localFile, remoteFilePath);

                    if (success)
                        successCount++;
                }
            }

            return successCount;
        }

        /// <summary>
        /// 批量下载文件
        /// </summary>
        /// <param name="remoteFiles">远程文件路径列表</param>
        /// <param name="localDirectory">本地目录</param>
        /// <param name="useResume">是否使用断点续传</param>
        /// <returns>下载成功的文件数量</returns>
        public int BatchDownloadFiles(List<string> remoteFiles, string localDirectory, bool useResume = false)
        {
            int successCount = 0;

            if (!Directory.Exists(localDirectory))
                Directory.CreateDirectory(localDirectory);

            foreach (string remoteFile in remoteFiles)
            {
                string fileName = Path.GetFileName(remoteFile);
                string localFilePath = Path.Combine(localDirectory, fileName);

                bool success = useResume ?
                    DownloadFileWithResume(remoteFile, localFilePath) :
                    DownloadFile(remoteFile, localFilePath);

                if (success)
                    successCount++;
            }

            return successCount;
        }

        /// <summary>
        /// 获取详细的错误信息
        /// </summary>
        /// <param name="ex">异常对象</param>
        /// <returns>错误信息</returns>
        public static string GetDetailedErrorMessage(Exception ex)
        {
            var webEx = ex as WebException;
            if (webEx != null)
            {
                var ftpResponse = webEx.Response as FtpWebResponse;
                if (ftpResponse != null)
                {
                    return $"FTP错误: {ftpResponse.StatusCode} - {ftpResponse.StatusDescription}";
                }
            }
            return ex.Message;
        }
        #endregion
    }
}