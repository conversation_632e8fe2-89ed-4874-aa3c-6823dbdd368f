﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="dms_config.aspx.cs" Inherits="DMS.Web.dms.dms_config" StylesheetTheme="Admin_Default" EnableEventValidation="false" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>文档管理系统配置</title>
    <script src="/js/jquery-1.8.3.min.js"></script>
    <script src="/admin/js/Common.js"></script>
    <script src="/js/layer/layer.js"></script>
    <style>
        .wrap { max-width: 1000px; background-color: white; padding: 20px; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
        .header { margin-bottom: 20px; padding-bottom: 10px; border-bottom: 2px solid #007bff; }
        .header h2 { margin: 0; font-size: 18px; }
        .config-info { background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        .config-item { margin-bottom: 10px; }
        .config-item label { display: inline-block; width: 120px; font-weight: bold; color: #333; }
        .config-item span { color: #666; }
        .no-config { text-align: center; color: #999; padding: 40px; }
        .password-mask { color: #999; }
    </style>
</head>
<body>
    <form id="form1" runat="server">
        <div class="container">
            <div class="wrap">
                <div class="header">
                    <h2>文档管理系统配置</h2>
                </div>

                <asp:Panel ID="pnlConfig" runat="server" Visible="false">
                    <div class="config-info">
                        <h3>当前配置信息</h3>
                        <div class="config-item">
                            <label>服务器地址:</label>
                            <span>
                                <asp:Literal ID="litServerUrl" runat="server"></asp:Literal></span>
                        </div>
                        <div class="config-item">
                            <label>FTP端口:</label>
                            <span>
                                <asp:Literal ID="litFtpPort" runat="server"></asp:Literal></span>
                        </div>
                        <div class="config-item">
                            <label>FTP账号:</label>
                            <span>
                                <asp:Literal ID="litFtpAccount" runat="server"></asp:Literal></span>
                        </div>
                        <div class="config-item">
                            <label>FTP密码:</label>
                            <span class="password-mask">
                                <asp:Literal ID="litFtpPassword" runat="server"></asp:Literal></span>
                        </div>
                        <div class="config-item">
                            <label>最后修改时间:</label>
                            <span>
                                <asp:Literal ID="litLastEditTime" runat="server"></asp:Literal></span>
                        </div>
                    </div>
                    <div>
                        <input id="btnEdit" type="button" value="编辑配置" class="btn" />
                    </div>
                </asp:Panel>

                <asp:Panel ID="pnlNoConfig" runat="server" Visible="false">
                    <div class="no-config">
                        <h3>暂无配置信息</h3>
                        <p>当前地区/学校还没有配置上传服务器信息</p>
                        <input id="btnAdd" type="button" value="添加配置" class="btnGreen" />
                    </div>
                </asp:Panel>
            </div>
        </div>
        <asp:ScriptManager ID="ScriptManager1" runat="server"></asp:ScriptManager>
        <asp:Button ID="btnSearch" runat="server" Text="" Style="opacity: 0; display: none;" OnClick="btnSearch_Click" />
    </form>
    <script>
        function showPage(title, url) {
            layer.open({
                type: 2,
                title: title,
                area: ['600px', '550px'],
                content: url,
            });
        }
        $(function () {
            $('#btnAdd').click(function () {
                var title = '系统配置';
                var url = "dms_config_edit.aspx";
                showPage(title, url)
                return false;
            });
            $('#btnEdit').click(function () {
                var title = '系统配置';
                var url = "dms_config_edit.aspx";
                showPage(title, url)
                return false;
            });
        });
    </script>
</body>
</html>
